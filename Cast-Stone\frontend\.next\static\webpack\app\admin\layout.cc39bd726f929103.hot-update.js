"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/layout",{

/***/ "(app-pages-browser)/./src/components/admin/AdminSidebar.tsx":
/*!***********************************************!*\
  !*** ./src/components/admin/AdminSidebar.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,LayoutDashboard,LogOut,Package,Settings,Shield,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,LayoutDashboard,LogOut,Package,Settings,Shield,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,LayoutDashboard,LogOut,Package,Settings,Shield,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,LayoutDashboard,LogOut,Package,Settings,Shield,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,LayoutDashboard,LogOut,Package,Settings,Shield,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,LayoutDashboard,LogOut,Package,Settings,Shield,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,LayoutDashboard,LogOut,Package,Settings,Shield,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,LayoutDashboard,LogOut,Package,Settings,Shield,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,LayoutDashboard,LogOut,Package,Settings,Shield,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,ChevronLeft,ChevronRight,LayoutDashboard,LogOut,Package,Settings,Shield,ShoppingCart,Users!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _contexts_AdminContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../contexts/AdminContext */ \"(app-pages-browser)/./src/contexts/AdminContext.tsx\");\n/* harmony import */ var _AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AdminSidebar.module.css */ \"(app-pages-browser)/./src/components/admin/AdminSidebar.module.css\");\n/* harmony import */ var _AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst menuItems = [\n    {\n        id: 'dashboard',\n        label: 'Dashboard',\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        href: '/admin/dashboard'\n    },\n    {\n        id: 'products',\n        label: 'Products',\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        href: '/admin/products',\n        permission: {\n            resource: 'products',\n            action: 'read'\n        }\n    },\n    {\n        id: 'orders',\n        label: 'Orders',\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        href: '/admin/orders',\n        permission: {\n            resource: 'orders',\n            action: 'read'\n        }\n    },\n    {\n        id: 'users',\n        label: 'Users',\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        href: '/admin/users',\n        permission: {\n            resource: 'users',\n            action: 'read'\n        }\n    },\n    {\n        id: 'analytics',\n        label: 'Analytics',\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        href: '/admin/analytics',\n        permission: {\n            resource: 'analytics',\n            action: 'read'\n        }\n    },\n    {\n        id: 'admins',\n        label: 'Admin Users',\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        href: '/admin/admin-users',\n        permission: {\n            resource: 'admins',\n            action: 'read'\n        }\n    },\n    {\n        id: 'settings',\n        label: 'Settings',\n        icon: _barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        href: '/admin/settings'\n    }\n];\nfunction AdminSidebar() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { state, dispatch, hasPermission, logout } = (0,_contexts_AdminContext__WEBPACK_IMPORTED_MODULE_3__.useAdmin)();\n    const { sidebarCollapsed } = state;\n    const toggleSidebar = ()=>{\n        dispatch({\n            type: 'TOGGLE_SIDEBAR'\n        });\n    };\n    const handleLogout = ()=>{\n        if (confirm('Are you sure you want to logout?')) {\n            logout();\n        }\n    };\n    const filteredMenuItems = menuItems.filter((item)=>{\n        if (!item.permission) return true;\n        return hasPermission(item.permission.resource, item.permission.action);\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"\".concat((_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().sidebar), \" \").concat(sidebarCollapsed ? (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().collapsed) : ''),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logo),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoIcon)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoText),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoTitle),\n                                        children: \"Cast Stone\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoSubtitle),\n                                        children: \"Admin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().toggleButton),\n                        onClick: toggleSidebar,\n                        \"aria-label\": sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar',\n                        children: sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 31\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 50\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().nav),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuList),\n                    children: filteredMenuItems.map((item)=>{\n                        const Icon = item.icon;\n                        const isActive = pathname === item.href || pathname.startsWith(item.href + '/');\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuItem),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: item.href,\n                                className: \"\".concat((_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuLink), \" \").concat(isActive ? (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().active) : ''),\n                                title: sidebarCollapsed ? item.label : undefined,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuIcon)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 19\n                                    }, this),\n                                    !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuLabel),\n                                                children: item.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 23\n                                            }, this),\n                                            item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuBadge),\n                                                children: item.badge\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 17\n                            }, this)\n                        }, item.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().footer),\n                children: [\n                    state.admin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().adminInfo),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().adminAvatar),\n                                children: state.admin.name.charAt(0).toUpperCase()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this),\n                            !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().adminDetails),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().adminName),\n                                        children: state.admin.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().adminRole),\n                                        children: state.admin.role\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoutButton),\n                        onClick: handleLogout,\n                        title: sidebarCollapsed ? 'Logout' : undefined,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_ChevronLeft_ChevronRight_LayoutDashboard_LogOut_Package_Settings_Shield_ShoppingCart_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: (_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoutIcon)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this),\n                            !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Logout\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 33\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminSidebar, \"K9F98orDTnQZRyRefLPDSpCe0MQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _contexts_AdminContext__WEBPACK_IMPORTED_MODULE_3__.useAdmin\n    ];\n});\n_c = AdminSidebar;\nvar _c;\n$RefreshReg$(_c, \"AdminSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/AdminSidebar.tsx\n"));

/***/ })

});