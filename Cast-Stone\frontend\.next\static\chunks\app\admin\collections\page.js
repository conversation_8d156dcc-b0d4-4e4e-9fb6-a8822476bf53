/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/admin/collections/page"],{

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/Icon.js":
/*!*****************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/Icon.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Icon)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultAttributes.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/defaultAttributes.js\");\n/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/shared/src/utils.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\nconst Icon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(_c = (param, ref)=>{\n    let { color = \"currentColor\", size = 24, strokeWidth = 2, absoluteStrokeWidth, className = \"\", children, iconNode, ...rest } = param;\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"svg\", {\n        ref,\n        ..._defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n        className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(\"lucide\", className),\n        ...!children && !(0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.hasA11yProp)(rest) && {\n            \"aria-hidden\": \"true\"\n        },\n        ...rest\n    }, [\n        ...iconNode.map((param)=>{\n            let [tag, attrs] = param;\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(tag, attrs);\n        }),\n        ...Array.isArray(children) ? children : [\n            children\n        ]\n    ]);\n});\n_c1 = Icon;\n //# sourceMappingURL=Icon.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Icon$forwardRef\");\n$RefreshReg$(_c1, \"Icon\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/Icon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js":
/*!*****************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/createLucideIcon.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createLucideIcon)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/shared/src/utils.js\");\n/* harmony import */ var _Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Icon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/Icon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\nconst createLucideIcon = (iconName, iconNode)=>{\n    const Component = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((param, ref)=>{\n        let { className, ...props } = param;\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Icon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            ref,\n            iconNode,\n            className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(\"lucide-\".concat((0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toKebabCase)((0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toPascalCase)(iconName))), \"lucide-\".concat(iconName), className),\n            ...props\n        });\n    });\n    Component.displayName = (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toPascalCase)(iconName);\n    return Component;\n};\n //# sourceMappingURL=createLucideIcon.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2NyZWF0ZUx1Y2lkZUljb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQVdNLHVCQUFtQixHQUFDLFVBQWtCLFFBQXVCO0lBQ2pFLE1BQU0sQ0FBWSwyRUFBdUMsUUFBMEI7WUFBekIsRUFBRSxDQUFXLFdBQUcsUUFBUzs2QkFDakYsb0RBQWEsQ0FBQyxnREFBTTtZQUNsQjtZQUNBO1lBQ0EsU0FBVyxxRUFDQyxVQUFtQyxPQUFuQyxrRUFBWSxrRUFBYSxFQUFRLFFBQUMsQ0FBQyxHQUM3QyxRQUFVLEVBQVEsT0FBUixRQUFRLEdBQ2xCO1lBRUYsQ0FBRztRQUNKOztJQUdPLHdCQUFjLGtFQUFZLENBQUMsUUFBUTtJQUV0QztBQUNUIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXHNyY1xcY3JlYXRlTHVjaWRlSWNvbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVFbGVtZW50LCBmb3J3YXJkUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgbWVyZ2VDbGFzc2VzLCB0b0tlYmFiQ2FzZSwgdG9QYXNjYWxDYXNlIH0gZnJvbSAnQGx1Y2lkZS9zaGFyZWQnO1xuaW1wb3J0IHsgSWNvbk5vZGUsIEx1Y2lkZVByb3BzIH0gZnJvbSAnLi90eXBlcyc7XG5pbXBvcnQgSWNvbiBmcm9tICcuL0ljb24nO1xuXG4vKipcbiAqIENyZWF0ZSBhIEx1Y2lkZSBpY29uIGNvbXBvbmVudFxuICogQHBhcmFtIHtzdHJpbmd9IGljb25OYW1lXG4gKiBAcGFyYW0ge2FycmF5fSBpY29uTm9kZVxuICogQHJldHVybnMge0ZvcndhcmRSZWZFeG90aWNDb21wb25lbnR9IEx1Y2lkZUljb25cbiAqL1xuY29uc3QgY3JlYXRlTHVjaWRlSWNvbiA9IChpY29uTmFtZTogc3RyaW5nLCBpY29uTm9kZTogSWNvbk5vZGUpID0+IHtcbiAgY29uc3QgQ29tcG9uZW50ID0gZm9yd2FyZFJlZjxTVkdTVkdFbGVtZW50LCBMdWNpZGVQcm9wcz4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+XG4gICAgY3JlYXRlRWxlbWVudChJY29uLCB7XG4gICAgICByZWYsXG4gICAgICBpY29uTm9kZSxcbiAgICAgIGNsYXNzTmFtZTogbWVyZ2VDbGFzc2VzKFxuICAgICAgICBgbHVjaWRlLSR7dG9LZWJhYkNhc2UodG9QYXNjYWxDYXNlKGljb25OYW1lKSl9YCxcbiAgICAgICAgYGx1Y2lkZS0ke2ljb25OYW1lfWAsXG4gICAgICAgIGNsYXNzTmFtZSxcbiAgICAgICksXG4gICAgICAuLi5wcm9wcyxcbiAgICB9KSxcbiAgKTtcblxuICBDb21wb25lbnQuZGlzcGxheU5hbWUgPSB0b1Bhc2NhbENhc2UoaWNvbk5hbWUpO1xuXG4gIHJldHVybiBDb21wb25lbnQ7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBjcmVhdGVMdWNpZGVJY29uO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/defaultAttributes.js":
/*!******************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/defaultAttributes.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defaultAttributes)\n/* harmony export */ });\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ var defaultAttributes = {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 24,\n    height: 24,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: 2,\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n};\n //# sourceMappingURL=defaultAttributes.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2RlZmF1bHRBdHRyaWJ1dGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztJQUFBLENBQWU7SUFDYixLQUFPO0lBQ1AsS0FBTztJQUNQLE1BQVE7SUFDUixPQUFTO0lBQ1QsSUFBTTtJQUNOLE1BQVE7SUFDUixXQUFhO0lBQ2IsYUFBZTtJQUNmLGNBQWdCO0FBQ2xCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxQYXRyaWNrcyB3ZWJcXHNyY1xcZGVmYXVsdEF0dHJpYnV0ZXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1xuICB4bWxuczogJ2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyxcbiAgd2lkdGg6IDI0LFxuICBoZWlnaHQ6IDI0LFxuICB2aWV3Qm94OiAnMCAwIDI0IDI0JyxcbiAgZmlsbDogJ25vbmUnLFxuICBzdHJva2U6ICdjdXJyZW50Q29sb3InLFxuICBzdHJva2VXaWR0aDogMixcbiAgc3Ryb2tlTGluZWNhcDogJ3JvdW5kJyxcbiAgc3Ryb2tlTGluZWpvaW46ICdyb3VuZCcsXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/defaultAttributes.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-down.js":
/*!*******************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/chevron-down.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChevronDown)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m6 9 6 6 6-6\",\n            key: \"qrunsl\"\n        }\n    ]\n];\nconst ChevronDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-down\", __iconNode);\n //# sourceMappingURL=chevron-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2NoZXZyb24tZG93bi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHYSxtQkFBdUI7SUFBQztRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsY0FBZ0I7WUFBQSxJQUFLLFNBQVM7UUFBQSxDQUFDO0tBQUM7Q0FBQTtBQWE3RSxrQkFBYyxrRUFBaUIsaUJBQWdCLENBQVUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVW1lciBGYXJvb3FcXERlc2t0b3BcXHNyY1xcaWNvbnNcXGNoZXZyb24tZG93bi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbWydwYXRoJywgeyBkOiAnbTYgOSA2IDYgNi02Jywga2V5OiAncXJ1bnNsJyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaGV2cm9uRG93blxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0p0TmlBNUlEWWdOaUEyTFRZaUlDOCtDand2YzNablBnbz0pIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2NoZXZyb24tZG93blxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENoZXZyb25Eb3duID0gY3JlYXRlTHVjaWRlSWNvbignY2hldnJvbi1kb3duJywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IENoZXZyb25Eb3duO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-right.js":
/*!********************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/chevron-right.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ ChevronRight)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m9 18 6-6-6-6\",\n            key: \"mthhwq\"\n        }\n    ]\n];\nconst ChevronRight = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"chevron-right\", __iconNode);\n //# sourceMappingURL=chevron-right.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2NoZXZyb24tcmlnaHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR2EsbUJBQXVCO0lBQUM7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLGVBQWlCO1lBQUEsSUFBSyxTQUFTO1FBQUEsQ0FBQztLQUFDO0NBQUE7QUFhOUUsbUJBQWUsa0VBQWlCLGtCQUFpQixDQUFVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVtZXIgRmFyb29xXFxEZXNrdG9wXFxzcmNcXGljb25zXFxjaGV2cm9uLXJpZ2h0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuaW1wb3J0IHsgSWNvbk5vZGUgfSBmcm9tICcuLi90eXBlcyc7XG5cbmV4cG9ydCBjb25zdCBfX2ljb25Ob2RlOiBJY29uTm9kZSA9IFtbJ3BhdGgnLCB7IGQ6ICdtOSAxOCA2LTYtNi02Jywga2V5OiAnbXRoaHdxJyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaGV2cm9uUmlnaHRcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKdE9TQXhPQ0EyTFRZdE5pMDJJaUF2UGdvOEwzTjJaejRLKSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9jaGV2cm9uLXJpZ2h0XG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2hldnJvblJpZ2h0ID0gY3JlYXRlTHVjaWRlSWNvbignY2hldnJvbi1yaWdodCcsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgZGVmYXVsdCBDaGV2cm9uUmlnaHQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-right.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/circle-alert.js":
/*!*******************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/circle-alert.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ CircleAlert)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"8\",\n            y2: \"12\",\n            key: \"1pkeuh\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12.01\",\n            y1: \"16\",\n            y2: \"16\",\n            key: \"4dfq90\"\n        }\n    ]\n];\nconst CircleAlert = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"circle-alert\", __iconNode);\n //# sourceMappingURL=circle-alert.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/circle-alert.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/eye.js":
/*!**********************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/eye.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Eye)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0\",\n            key: \"1nclc0\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"3\",\n            key: \"1v7zrd\"\n        }\n    ]\n];\nconst Eye = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"eye\", __iconNode);\n //# sourceMappingURL=eye.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/eye.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/folder-open.js":
/*!******************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/folder-open.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ FolderOpen)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2\",\n            key: \"usdka0\"\n        }\n    ]\n];\nconst FolderOpen = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"folder-open\", __iconNode);\n //# sourceMappingURL=folder-open.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/folder-open.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/folder-tree.js":
/*!******************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/folder-tree.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ FolderTree)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M20 10a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1h-2.5a1 1 0 0 1-.8-.4l-.9-1.2A1 1 0 0 0 15 3h-2a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1Z\",\n            key: \"hod4my\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M20 21a1 1 0 0 0 1-1v-3a1 1 0 0 0-1-1h-2.9a1 1 0 0 1-.88-.55l-.42-.85a1 1 0 0 0-.92-.6H13a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1Z\",\n            key: \"w4yl2u\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 5a2 2 0 0 0 2 2h3\",\n            key: \"f2jnh7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 3v13a2 2 0 0 0 2 2h3\",\n            key: \"k8epm1\"\n        }\n    ]\n];\nconst FolderTree = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"folder-tree\", __iconNode);\n //# sourceMappingURL=folder-tree.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/folder-tree.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/folder.js":
/*!*************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/folder.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Folder)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z\",\n            key: \"1kt360\"\n        }\n    ]\n];\nconst Folder = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"folder\", __iconNode);\n //# sourceMappingURL=folder.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/folder.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/package.js":
/*!**************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/package.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Package)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z\",\n            key: \"1a0edw\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 22V12\",\n            key: \"d0xqtd\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"3.29 7 12 12 20.71 7\",\n            key: \"ousv84\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m7.5 4.27 9 5.15\",\n            key: \"1c824w\"\n        }\n    ]\n];\nconst Package = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"package\", __iconNode);\n //# sourceMappingURL=package.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/package.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/plus.js":
/*!***********************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/plus.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Plus)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M5 12h14\",\n            key: \"1ays0h\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 5v14\",\n            key: \"s699le\"\n        }\n    ]\n];\nconst Plus = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"plus\", __iconNode);\n //# sourceMappingURL=plus.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/plus.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/square-pen.js":
/*!*****************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/square-pen.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ SquarePen)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\",\n            key: \"1m0v6g\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z\",\n            key: \"ohrbg2\"\n        }\n    ]\n];\nconst SquarePen = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"square-pen\", __iconNode);\n //# sourceMappingURL=square-pen.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/square-pen.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trash-2.js":
/*!**************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/trash-2.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Trash2)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M3 6h18\",\n            key: \"d0wm0j\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\",\n            key: \"4alrt4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\",\n            key: \"v07s0e\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"10\",\n            x2: \"10\",\n            y1: \"11\",\n            y2: \"17\",\n            key: \"1uufr5\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"14\",\n            x2: \"14\",\n            y1: \"11\",\n            y2: \"17\",\n            key: \"xtxkd\"\n        }\n    ]\n];\nconst Trash2 = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"trash-2\", __iconNode);\n //# sourceMappingURL=trash-2.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trash-2.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/x.js":
/*!********************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/icons/x.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ X)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M18 6 6 18\",\n            key: \"1bl5f8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m6 6 12 12\",\n            key: \"d8bk6v\"\n        }\n    ]\n];\nconst X = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"x\", __iconNode);\n //# sourceMappingURL=x.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/x.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/lucide-react/dist/esm/shared/src/utils.js":
/*!*****************************************************************!*\
  !*** ../node_modules/lucide-react/dist/esm/shared/src/utils.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasA11yProp: () => (/* binding */ hasA11yProp),\n/* harmony export */   mergeClasses: () => (/* binding */ mergeClasses),\n/* harmony export */   toCamelCase: () => (/* binding */ toCamelCase),\n/* harmony export */   toKebabCase: () => (/* binding */ toKebabCase),\n/* harmony export */   toPascalCase: () => (/* binding */ toPascalCase)\n/* harmony export */ });\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ const toKebabCase = (string)=>string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst toCamelCase = (string)=>string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2)=>p2 ? p2.toUpperCase() : p1.toLowerCase());\nconst toPascalCase = (string)=>{\n    const camelCase = toCamelCase(string);\n    return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);\n};\nconst mergeClasses = function() {\n    for(var _len = arguments.length, classes = new Array(_len), _key = 0; _key < _len; _key++){\n        classes[_key] = arguments[_key];\n    }\n    return classes.filter((className, index, array)=>{\n        return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n    }).join(\" \").trim();\n};\nconst hasA11yProp = (props)=>{\n    for(const prop in props){\n        if (prop.startsWith(\"aria-\") || prop === \"role\" || prop === \"title\") {\n            return true;\n        }\n    }\n};\n //# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/lucide-react/dist/esm/shared/src/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Ccollections%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Ccollections%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/admin/collections/page.tsx */ \"(app-pages-browser)/./src/app/admin/collections/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVW1lciUyMEZhcm9vcSU1QyU1Q0Rlc2t0b3AlNUMlNUNQYXRyaWNrcyUyMHdlYiU1QyU1Q0Nhc3QtU3RvbmUlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2FkbWluJTVDJTVDY29sbGVjdGlvbnMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLGtNQUFnSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVW1lciBGYXJvb3FcXFxcRGVza3RvcFxcXFxQYXRyaWNrcyB3ZWJcXFxcQ2FzdC1TdG9uZVxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGFkbWluXFxcXGNvbGxlY3Rpb25zXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Ccollections%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={529:(e,r,t)=>{var n=t(191);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},191:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(529);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvbWluaS1jc3MtZXh0cmFjdC1wbHVnaW4vaG1yL2hvdE1vZHVsZVJlcGxhY2VtZW50LmpzIiwibWFwcGluZ3MiOiI7QUFBQSxNQUFNLGFBQWEsT0FBTyxjQUFjLGFBQWEsMEJBQTBCLG9DQUFvQyw4QkFBOEIsdUJBQXVCLFFBQVEsa0JBQWtCLFdBQVcsZ0JBQWdCLDhCQUE4QixxQkFBcUIsZ0JBQWdCLG1CQUFtQixpQkFBaUIsZ0NBQWdDLFdBQVcsT0FBTywyQkFBMkIsNkJBQTZCLEtBQUssOENBQThDLG9CQUFvQixNQUFNLFNBQVMsT0FBTyxtQkFBbUIsT0FBTyxZQUFZLGdDQUFnQyxjQUFjLE9BQU8sZ0NBQWdDLE9BQU8sZ0NBQWdDLHFDQUFxQyw0Q0FBNEMsMkNBQTJDLFNBQVMsZ0JBQWdCLElBQUksd0JBQXdCLE9BQU8sWUFBWSxPQUFPLHVCQUF1QixxQkFBcUIsT0FBTyx1QkFBdUIsT0FBTyxnQ0FBZ0MsT0FBTyxlQUFlLG9CQUFvQixpQkFBaUIsc0NBQXNDLGVBQWUsT0FBTyxnQkFBZ0IsNEJBQTRCLEdBQUcsdUNBQXVDLGVBQWUsT0FBTyxnQkFBZ0IsNEJBQTRCLEdBQUcsMkNBQTJDLGtCQUFrQiwyQ0FBMkMsS0FBSyw2QkFBNkIsMkJBQTJCLE1BQU0sT0FBTyxlQUFlLEVBQUUsb0JBQW9CLG9CQUFvQixLQUFLLEdBQUcsU0FBUyx3QkFBd0IsT0FBTyxhQUFhLHdDQUF3QyxZQUFZLHNCQUFzQixZQUFZLE9BQU8sNkJBQTZCLHFCQUFxQixPQUFPLHFCQUFxQixPQUFPLE1BQU0sZUFBZSxRQUFRLEdBQUcsU0FBUyxxQkFBcUIsd0NBQXdDLHNCQUFzQixxQkFBcUIsT0FBTyxhQUFhLEdBQUcseUJBQXlCLHlDQUF5QyxhQUFhLFlBQVksd0JBQXdCLE1BQU0sMERBQTBELFlBQVksNkJBQTZCLGtCQUFrQixvQkFBb0IscUJBQXFCLGFBQWEsZ0VBQWdFLFlBQVksT0FBTyxNQUFNLCtDQUErQyxLQUFLLG9DQUFvQyxhQUFhLDRCQUE0QixTQUFTLHlCQUF5QiwrQkFBK0IsVUFBVSxpQkFBaUIsTUFBTSxjQUFjLGtCQUFrQixTQUFTLGdCQUFnQixzQkFBc0IsV0FBVyxzQkFBc0IsU0FBUyxvREFBb0QsaURBQWlELDJDQUEyQyxRQUFRLHNCQUFzQixnQkFBZ0IsU0FBUyxnQ0FBZ0MsV0FBVyxrQkFBa0IsaUJBQWlCLFlBQVksWUFBWSxXQUFXLElBQUksc0NBQXNDLFFBQVEsUUFBUSxpQkFBaUIsaUJBQWlCLG1FQUFtRSxTQUFTLEtBQUssK0JBQStCLGlCQUFpQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVbWVyIEZhcm9vcVxcRGVza3RvcFxcUGF0cmlja3Mgd2ViXFxDYXN0LVN0b25lXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxcbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5cXGhtclxcaG90TW9kdWxlUmVwbGFjZW1lbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiKCgpPT57XCJ1c2Ugc3RyaWN0XCI7dmFyIGU9ezUyOTooZSxyLHQpPT57dmFyIG49dCgxOTEpO3ZhciBpPU9iamVjdC5jcmVhdGUobnVsbCk7dmFyIGE9dHlwZW9mIGRvY3VtZW50PT09XCJ1bmRlZmluZWRcIjt2YXIgbz1BcnJheS5wcm90b3R5cGUuZm9yRWFjaDtmdW5jdGlvbiBkZWJvdW5jZShlLHIpe3ZhciB0PTA7cmV0dXJuIGZ1bmN0aW9uKCl7dmFyIG49dGhpczt2YXIgaT1hcmd1bWVudHM7dmFyIGE9ZnVuY3Rpb24gZnVuY3Rpb25DYWxsKCl7cmV0dXJuIGUuYXBwbHkobixpKX07Y2xlYXJUaW1lb3V0KHQpO3Q9c2V0VGltZW91dChhLHIpfX1mdW5jdGlvbiBub29wKCl7fWZ1bmN0aW9uIGdldEN1cnJlbnRTY3JpcHRVcmwoZSl7dmFyIHI9aVtlXTtpZighcil7aWYoZG9jdW1lbnQuY3VycmVudFNjcmlwdCl7cj1kb2N1bWVudC5jdXJyZW50U2NyaXB0LnNyY31lbHNle3ZhciB0PWRvY3VtZW50LmdldEVsZW1lbnRzQnlUYWdOYW1lKFwic2NyaXB0XCIpO3ZhciBhPXRbdC5sZW5ndGgtMV07aWYoYSl7cj1hLnNyY319aVtlXT1yfXJldHVybiBmdW5jdGlvbihlKXtpZighcil7cmV0dXJuIG51bGx9dmFyIHQ9ci5zcGxpdCgvKFteXFxcXC9dKylcXC5qcyQvKTt2YXIgaT10JiZ0WzFdO2lmKCFpKXtyZXR1cm5bci5yZXBsYWNlKFwiLmpzXCIsXCIuY3NzXCIpXX1pZighZSl7cmV0dXJuW3IucmVwbGFjZShcIi5qc1wiLFwiLmNzc1wiKV19cmV0dXJuIGUuc3BsaXQoXCIsXCIpLm1hcCgoZnVuY3Rpb24oZSl7dmFyIHQ9bmV3IFJlZ0V4cChcIlwiLmNvbmNhdChpLFwiXFxcXC5qcyRcIiksXCJnXCIpO3JldHVybiBuKHIucmVwbGFjZSh0LFwiXCIuY29uY2F0KGUucmVwbGFjZSgve2ZpbGVOYW1lfS9nLGkpLFwiLmNzc1wiKSkpfSkpfX1mdW5jdGlvbiB1cGRhdGVDc3MoZSxyKXtpZighcil7aWYoIWUuaHJlZil7cmV0dXJufXI9ZS5ocmVmLnNwbGl0KFwiP1wiKVswXX1pZighaXNVcmxSZXF1ZXN0KHIpKXtyZXR1cm59aWYoZS5pc0xvYWRlZD09PWZhbHNlKXtyZXR1cm59aWYoIXJ8fCEoci5pbmRleE9mKFwiLmNzc1wiKT4tMSkpe3JldHVybn1lLnZpc2l0ZWQ9dHJ1ZTt2YXIgdD1lLmNsb25lTm9kZSgpO3QuaXNMb2FkZWQ9ZmFsc2U7dC5hZGRFdmVudExpc3RlbmVyKFwibG9hZFwiLChmdW5jdGlvbigpe2lmKHQuaXNMb2FkZWQpe3JldHVybn10LmlzTG9hZGVkPXRydWU7ZS5wYXJlbnROb2RlLnJlbW92ZUNoaWxkKGUpfSkpO3QuYWRkRXZlbnRMaXN0ZW5lcihcImVycm9yXCIsKGZ1bmN0aW9uKCl7aWYodC5pc0xvYWRlZCl7cmV0dXJufXQuaXNMb2FkZWQ9dHJ1ZTtlLnBhcmVudE5vZGUucmVtb3ZlQ2hpbGQoZSl9KSk7dC5ocmVmPVwiXCIuY29uY2F0KHIsXCI/XCIpLmNvbmNhdChEYXRlLm5vdygpKTtpZihlLm5leHRTaWJsaW5nKXtlLnBhcmVudE5vZGUuaW5zZXJ0QmVmb3JlKHQsZS5uZXh0U2libGluZyl9ZWxzZXtlLnBhcmVudE5vZGUuYXBwZW5kQ2hpbGQodCl9fWZ1bmN0aW9uIGdldFJlbG9hZFVybChlLHIpe3ZhciB0O2U9bihlLHtzdHJpcFdXVzpmYWxzZX0pO3Iuc29tZSgoZnVuY3Rpb24obil7aWYoZS5pbmRleE9mKHIpPi0xKXt0PW59fSkpO3JldHVybiB0fWZ1bmN0aW9uIHJlbG9hZFN0eWxlKGUpe2lmKCFlKXtyZXR1cm4gZmFsc2V9dmFyIHI9ZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbChcImxpbmtcIik7dmFyIHQ9ZmFsc2U7by5jYWxsKHIsKGZ1bmN0aW9uKHIpe2lmKCFyLmhyZWYpe3JldHVybn12YXIgbj1nZXRSZWxvYWRVcmwoci5ocmVmLGUpO2lmKCFpc1VybFJlcXVlc3Qobikpe3JldHVybn1pZihyLnZpc2l0ZWQ9PT10cnVlKXtyZXR1cm59aWYobil7dXBkYXRlQ3NzKHIsbik7dD10cnVlfX0pKTtyZXR1cm4gdH1mdW5jdGlvbiByZWxvYWRBbGwoKXt2YXIgZT1kb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKFwibGlua1wiKTtvLmNhbGwoZSwoZnVuY3Rpb24oZSl7aWYoZS52aXNpdGVkPT09dHJ1ZSl7cmV0dXJufXVwZGF0ZUNzcyhlKX0pKX1mdW5jdGlvbiBpc1VybFJlcXVlc3QoZSl7aWYoIS9eW2EtekEtWl1bYS16QS1aXFxkK1xcLS5dKjovLnRlc3QoZSkpe3JldHVybiBmYWxzZX1yZXR1cm4gdHJ1ZX1lLmV4cG9ydHM9ZnVuY3Rpb24oZSxyKXtpZihhKXtjb25zb2xlLmxvZyhcIm5vIHdpbmRvdy5kb2N1bWVudCBmb3VuZCwgd2lsbCBub3QgSE1SIENTU1wiKTtyZXR1cm4gbm9vcH12YXIgdD1nZXRDdXJyZW50U2NyaXB0VXJsKGUpO2Z1bmN0aW9uIHVwZGF0ZSgpe3ZhciBlPXQoci5maWxlbmFtZSk7dmFyIG49cmVsb2FkU3R5bGUoZSk7aWYoci5sb2NhbHMpe2NvbnNvbGUubG9nKFwiW0hNUl0gRGV0ZWN0ZWQgbG9jYWwgY3NzIG1vZHVsZXMuIFJlbG9hZCBhbGwgY3NzXCIpO3JlbG9hZEFsbCgpO3JldHVybn1pZihuKXtjb25zb2xlLmxvZyhcIltITVJdIGNzcyByZWxvYWQgJXNcIixlLmpvaW4oXCIgXCIpKX1lbHNle2NvbnNvbGUubG9nKFwiW0hNUl0gUmVsb2FkIGFsbCBjc3NcIik7cmVsb2FkQWxsKCl9fXJldHVybiBkZWJvdW5jZSh1cGRhdGUsNTApfX0sMTkxOmU9PntmdW5jdGlvbiBub3JtYWxpemVVcmwoZSl7cmV0dXJuIGUucmVkdWNlKChmdW5jdGlvbihlLHIpe3N3aXRjaChyKXtjYXNlXCIuLlwiOmUucG9wKCk7YnJlYWs7Y2FzZVwiLlwiOmJyZWFrO2RlZmF1bHQ6ZS5wdXNoKHIpfXJldHVybiBlfSksW10pLmpvaW4oXCIvXCIpfWUuZXhwb3J0cz1mdW5jdGlvbihlKXtlPWUudHJpbSgpO2lmKC9eZGF0YTovaS50ZXN0KGUpKXtyZXR1cm4gZX12YXIgcj1lLmluZGV4T2YoXCIvL1wiKSE9PS0xP2Uuc3BsaXQoXCIvL1wiKVswXStcIi8vXCI6XCJcIjt2YXIgdD1lLnJlcGxhY2UobmV3IFJlZ0V4cChyLFwiaVwiKSxcIlwiKS5zcGxpdChcIi9cIik7dmFyIG49dFswXS50b0xvd2VyQ2FzZSgpLnJlcGxhY2UoL1xcLiQvLFwiXCIpO3RbMF09XCJcIjt2YXIgaT1ub3JtYWxpemVVcmwodCk7cmV0dXJuIHIrbitpfX19O3ZhciByPXt9O2Z1bmN0aW9uIF9fbmNjd3Bja19yZXF1aXJlX18odCl7dmFyIG49clt0XTtpZihuIT09dW5kZWZpbmVkKXtyZXR1cm4gbi5leHBvcnRzfXZhciBpPXJbdF09e2V4cG9ydHM6e319O3ZhciBhPXRydWU7dHJ5e2VbdF0oaSxpLmV4cG9ydHMsX19uY2N3cGNrX3JlcXVpcmVfXyk7YT1mYWxzZX1maW5hbGx5e2lmKGEpZGVsZXRlIHJbdF19cmV0dXJuIGkuZXhwb3J0c31pZih0eXBlb2YgX19uY2N3cGNrX3JlcXVpcmVfXyE9PVwidW5kZWZpbmVkXCIpX19uY2N3cGNrX3JlcXVpcmVfXy5hYj1fX2Rpcm5hbWUrXCIvXCI7dmFyIHQ9X19uY2N3cGNrX3JlcXVpcmVfXyg1MjkpO21vZHVsZS5leHBvcnRzPXR9KSgpOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVW1lciBGYXJvb3FcXERlc2t0b3BcXFBhdHJpY2tzIHdlYlxcQ2FzdC1TdG9uZVxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/admin/collections/page.module.css":
/*!***************************************************!*\
  !*** ./src/app/admin/collections/page.module.css ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"collectionsPage\":\"page_collectionsPage__G6y4A\",\"header\":\"page_header__2FUEd\",\"title\":\"page_title__DrTlr\",\"subtitle\":\"page_subtitle__x0rin\",\"headerActions\":\"page_headerActions__cszDS\",\"primaryButton\":\"page_primaryButton__etF9g\",\"secondaryButton\":\"page_secondaryButton__ZQjEx\",\"content\":\"page_content__aFiR9\",\"sidebar\":\"page_sidebar__Ub1d6\",\"mainContent\":\"page_mainContent__serbp\",\"collectionDetails\":\"page_collectionDetails__3IXqJ\",\"detailsHeader\":\"page_detailsHeader__kqpXG\",\"collectionInfo\":\"page_collectionInfo__DiRoi\",\"collectionMeta\":\"page_collectionMeta__tGpKZ\",\"metaItem\":\"page_metaItem__2fgdT\",\"statusBadge\":\"page_statusBadge__OpOQo\",\"published\":\"page_published__0ZkVw\",\"draft\":\"page_draft__PFOvN\",\"detailsActions\":\"page_detailsActions__6RkGe\",\"detailsContent\":\"page_detailsContent__xI67v\",\"descriptionSection\":\"page_descriptionSection__uocng\",\"statsSection\":\"page_statsSection__9KrIn\",\"childrenSection\":\"page_childrenSection__nhamY\",\"statsGrid\":\"page_statsGrid__XjzQI\",\"statCard\":\"page_statCard__ouQOq\",\"statValue\":\"page_statValue__w3FaM\",\"statLabel\":\"page_statLabel__l4UhV\",\"childrenGrid\":\"page_childrenGrid__wDS9i\",\"childCard\":\"page_childCard__w_aKx\",\"childIcon\":\"page_childIcon___PNaD\",\"childInfo\":\"page_childInfo__WCctG\",\"childStatus\":\"page_childStatus__5h3mu\",\"emptyState\":\"page_emptyState__V0Tkm\"};\n    if(true) {\n      // 1751482083964\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"605b6a244a6d\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/collections/page.module.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/admin/collections/page.tsx":
/*!********************************************!*\
  !*** ./src/app/admin/collections/page.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CollectionsManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_FolderTree_Package_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,FolderTree,Package,Plus!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_FolderTree_Package_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,FolderTree,Package,Plus!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_FolderTree_Package_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,FolderTree,Package,Plus!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/folder-tree.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_FolderTree_Package_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,FolderTree,Package,Plus!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_FolderTree_Package_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,FolderTree,Package,Plus!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contexts/AdminContext */ \"(app-pages-browser)/./src/contexts/AdminContext.tsx\");\n/* harmony import */ var _components_admin_CollectionHierarchy__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../components/admin/CollectionHierarchy */ \"(app-pages-browser)/./src/components/admin/CollectionHierarchy.tsx\");\n/* harmony import */ var _components_admin_CollectionModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../components/admin/CollectionModal */ \"(app-pages-browser)/./src/components/admin/CollectionModal.tsx\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./page.module.css */ \"(app-pages-browser)/./src/app/admin/collections/page.module.css\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_page_module_css__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction CollectionsManagement() {\n    var _selectedCollection_children;\n    _s();\n    const { hasPermission, addNotification } = (0,_contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__.useAdmin)();\n    const [selectedCollection, setSelectedCollection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCollectionModal, setShowCollectionModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [modalMode, setModalMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('create');\n    const [parentCollection, setParentCollection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshKey, setRefreshKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleSelectCollection = (collection)=>{\n        setSelectedCollection(collection);\n    };\n    const handleEditCollection = (collection)=>{\n        setSelectedCollection(collection);\n        setModalMode('edit');\n        setShowCollectionModal(true);\n    };\n    const handleDeleteCollection = async (collection)=>{\n        if (!hasPermission('products', 'delete')) {\n            addNotification({\n                type: 'error',\n                title: 'Permission Denied',\n                message: 'You do not have permission to delete collections'\n            });\n            return;\n        }\n        const confirmDelete = window.confirm('Are you sure you want to delete \"'.concat(collection.title, '\"? This action cannot be undone.'));\n        if (!confirmDelete) return;\n        try {\n            setIsLoading(true);\n            // TODO: Implement delete API call\n            // await collectionsApi.deleteCollection(collection._id);\n            addNotification({\n                type: 'success',\n                title: 'Collection Deleted',\n                message: '\"'.concat(collection.title, '\" has been deleted successfully')\n            });\n            setRefreshKey((prev)=>prev + 1);\n            if ((selectedCollection === null || selectedCollection === void 0 ? void 0 : selectedCollection._id) === collection._id) {\n                setSelectedCollection(null);\n            }\n        } catch (error) {\n            console.error('Failed to delete collection:', error);\n            addNotification({\n                type: 'error',\n                title: 'Delete Failed',\n                message: 'Failed to delete collection. Please try again.'\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddSubCollection = (parent)=>{\n        setParentCollection(parent);\n        setSelectedCollection(null);\n        setModalMode('create');\n        setShowCollectionModal(true);\n    };\n    const handleSaveCollection = async (collectionData)=>{\n        try {\n            setIsLoading(true);\n            if (modalMode === 'create') {\n                // TODO: Implement create API call\n                // await collectionsApi.createCollection(collectionData);\n                addNotification({\n                    type: 'success',\n                    title: 'Collection Created',\n                    message: '\"'.concat(collectionData.title, '\" has been created successfully')\n                });\n            } else {\n                // TODO: Implement update API call\n                // await collectionsApi.updateCollection(selectedCollection!._id, collectionData);\n                addNotification({\n                    type: 'success',\n                    title: 'Collection Updated',\n                    message: '\"'.concat(collectionData.title, '\" has been updated successfully')\n                });\n            }\n            setRefreshKey((prev)=>prev + 1);\n            setShowCollectionModal(false);\n        } catch (error) {\n            console.error('Failed to save collection:', error);\n            throw error; // Re-throw to let modal handle the error\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleCloseModal = ()=>{\n        setShowCollectionModal(false);\n        setSelectedCollection(null);\n        setParentCollection(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().collectionsPage),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().title),\n                                children: \"Collections Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().subtitle),\n                                children: \"Manage your hierarchical collection structure and organize products\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().headerActions),\n                        children: hasPermission('products', 'create') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().primaryButton),\n                            onClick: ()=>handleAddSubCollection(null),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_FolderTree_Package_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this),\n                                \"Add Root Collection\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().content),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().sidebar),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_CollectionHierarchy__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            onSelectCollection: handleSelectCollection,\n                            onEditCollection: handleEditCollection,\n                            onDeleteCollection: handleDeleteCollection,\n                            onAddSubCollection: handleAddSubCollection,\n                            selectedCollectionId: selectedCollection === null || selectedCollection === void 0 ? void 0 : selectedCollection._id\n                        }, refreshKey, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().mainContent),\n                        children: selectedCollection ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().collectionDetails),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().detailsHeader),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().collectionInfo),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    children: selectedCollection.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().collectionMeta),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().metaItem),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_FolderTree_Package_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Level \",\n                                                                selectedCollection.level\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().metaItem),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_FolderTree_Package_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    size: 16\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                selectedCollection.path\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().statusBadge), \" \").concat(selectedCollection.published ? (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().published) : (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().draft)),\n                                                            children: selectedCollection.published ? 'Published' : 'Draft'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().detailsActions),\n                                            children: [\n                                                hasPermission('products', 'update') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEditCollection(selectedCollection),\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().secondaryButton),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_FolderTree_Package_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Edit\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().secondaryButton),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_FolderTree_Package_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"View Products\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().detailsContent),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().descriptionSection),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: selectedCollection.description || 'No description provided.'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().statsSection),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    children: \"Statistics\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().statsGrid),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().statCard),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().statValue),\n                                                                    children: selectedCollection.productCount || 0\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().statLabel),\n                                                                    children: \"Products\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().statCard),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().statValue),\n                                                                    children: ((_selectedCollection_children = selectedCollection.children) === null || _selectedCollection_children === void 0 ? void 0 : _selectedCollection_children.length) || 0\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().statLabel),\n                                                                    children: \"Sub Collections\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().statCard),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().statValue),\n                                                                    children: selectedCollection.collectionType\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                                    lineNumber: 239,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().statLabel),\n                                                                    children: \"Type\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                                    lineNumber: 242,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this),\n                                        selectedCollection.children && selectedCollection.children.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().childrenSection),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    children: \"Sub Collections\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().childrenGrid),\n                                                    children: selectedCollection.children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().childCard),\n                                                            onClick: ()=>setSelectedCollection(child),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().childIcon),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_FolderTree_Package_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                        size: 20\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                                    lineNumber: 257,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().childInfo),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            children: child.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                                            lineNumber: 261,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: [\n                                                                                \"Level \",\n                                                                                child.level,\n                                                                                \" • \",\n                                                                                child.handle\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                                            lineNumber: 262,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().childStatus), \" \").concat(child.published ? (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().published) : (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().draft)),\n                                                                    children: child.published ? 'Published' : 'Draft'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, child._id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().emptyState),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_FolderTree_Package_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 64\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    children: \"Select a Collection\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Choose a collection from the hierarchy to view its details and manage its settings.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            showCollectionModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_CollectionModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: showCollectionModal,\n                onClose: handleCloseModal,\n                onSave: handleSaveCollection,\n                collection: modalMode === 'edit' ? selectedCollection : null,\n                parentCollection: parentCollection,\n                mode: modalMode\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\collections\\\\page.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, this);\n}\n_s(CollectionsManagement, \"Lf/loHTje8gxZXhppwy/3lp0Efk=\", false, function() {\n    return [\n        _contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__.useAdmin\n    ];\n});\n_c = CollectionsManagement;\nvar _c;\n$RefreshReg$(_c, \"CollectionsManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/collections/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/admin/CollectionHierarchy.module.css":
/*!*************************************************************!*\
  !*** ./src/components/admin/CollectionHierarchy.module.css ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"hierarchyContainer\":\"CollectionHierarchy_hierarchyContainer__lz8mn\",\"hierarchyHeader\":\"CollectionHierarchy_hierarchyHeader__Ekcmd\",\"addRootButton\":\"CollectionHierarchy_addRootButton__LUKk2\",\"hierarchyTree\":\"CollectionHierarchy_hierarchyTree__IWInT\",\"collectionNode\":\"CollectionHierarchy_collectionNode__J_Ag_\",\"collectionItem\":\"CollectionHierarchy_collectionItem__3nwho\",\"selected\":\"CollectionHierarchy_selected__ExALV\",\"collectionContent\":\"CollectionHierarchy_collectionContent__ItDfW\",\"expandButton\":\"CollectionHierarchy_expandButton__Tcc5i\",\"expandToggle\":\"CollectionHierarchy_expandToggle__bXKWi\",\"expandSpacer\":\"CollectionHierarchy_expandSpacer__ueF5Q\",\"collectionIcon\":\"CollectionHierarchy_collectionIcon__4adtE\",\"collectionInfo\":\"CollectionHierarchy_collectionInfo__6mUL4\",\"collectionTitle\":\"CollectionHierarchy_collectionTitle__Id281\",\"collectionMeta\":\"CollectionHierarchy_collectionMeta__sxym6\",\"collectionStatus\":\"CollectionHierarchy_collectionStatus__unblF\",\"statusBadge\":\"CollectionHierarchy_statusBadge__EWdjc\",\"published\":\"CollectionHierarchy_published__JPipe\",\"draft\":\"CollectionHierarchy_draft__sMVxV\",\"collectionActions\":\"CollectionHierarchy_collectionActions__3bLBq\",\"actionButton\":\"CollectionHierarchy_actionButton__ZLATT\",\"childrenContainer\":\"CollectionHierarchy_childrenContainer__uTTfw\",\"loadingContainer\":\"CollectionHierarchy_loadingContainer__qlNq2\",\"spinner\":\"CollectionHierarchy_spinner__6xDfJ\",\"spin\":\"CollectionHierarchy_spin__dSZzR\",\"errorContainer\":\"CollectionHierarchy_errorContainer__PRqOl\",\"errorMessage\":\"CollectionHierarchy_errorMessage__RgoGB\",\"retryButton\":\"CollectionHierarchy_retryButton__jTYdl\",\"emptyState\":\"CollectionHierarchy_emptyState__0W_fU\",\"addFirstButton\":\"CollectionHierarchy_addFirstButton__4gxx9\"};\n    if(true) {\n      // 1751482155768\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"69afdff6df6e\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/CollectionHierarchy.module.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/admin/CollectionHierarchy.tsx":
/*!******************************************************!*\
  !*** ./src/components/admin/CollectionHierarchy.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CollectionHierarchy)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,Package,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,Package,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,Package,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,Package,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,Package,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,Package,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,Package,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight,Edit,Folder,FolderOpen,Package,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CollectionHierarchy.module.css */ \"(app-pages-browser)/./src/components/admin/CollectionHierarchy.module.css\");\n/* harmony import */ var _CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CollectionHierarchy(param) {\n    let { onSelectCollection, onEditCollection, onDeleteCollection, onAddSubCollection, selectedCollectionId } = param;\n    var _this = this;\n    _s();\n    const [collections, setCollections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [expandedNodes, setExpandedNodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CollectionHierarchy.useEffect\": ()=>{\n            fetchCollectionHierarchy();\n        }\n    }[\"CollectionHierarchy.useEffect\"], []);\n    const fetchCollectionHierarchy = async ()=>{\n        try {\n            var _response_collections;\n            setIsLoading(true);\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_2__.collectionsApi.getCollectionHierarchy();\n            setCollections(response.collections || []);\n            // Auto-expand root collections\n            const rootIds = ((_response_collections = response.collections) === null || _response_collections === void 0 ? void 0 : _response_collections.map((c)=>c._id)) || [];\n            setExpandedNodes(new Set(rootIds));\n        } catch (error) {\n            console.error('Failed to fetch collection hierarchy:', error);\n            setError('Failed to load collection hierarchy');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleExpanded = (collectionId)=>{\n        const newExpanded = new Set(expandedNodes);\n        if (newExpanded.has(collectionId)) {\n            newExpanded.delete(collectionId);\n        } else {\n            newExpanded.add(collectionId);\n        }\n        setExpandedNodes(newExpanded);\n    };\n    const handleSelectCollection = (collection)=>{\n        onSelectCollection === null || onSelectCollection === void 0 ? void 0 : onSelectCollection(collection);\n    };\n    const handleEditCollection = (e, collection)=>{\n        e.stopPropagation();\n        onEditCollection === null || onEditCollection === void 0 ? void 0 : onEditCollection(collection);\n    };\n    const handleDeleteCollection = (e, collection)=>{\n        e.stopPropagation();\n        onDeleteCollection === null || onDeleteCollection === void 0 ? void 0 : onDeleteCollection(collection);\n    };\n    const handleAddSubCollection = (e, parentCollection)=>{\n        e.stopPropagation();\n        onAddSubCollection === null || onAddSubCollection === void 0 ? void 0 : onAddSubCollection(parentCollection);\n    };\n    const renderCollectionNode = function(collection) {\n        let depth = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        const isExpanded = expandedNodes.has(collection._id);\n        const isSelected = selectedCollectionId === collection._id;\n        const hasChildren = collection.children && collection.children.length > 0;\n        const canHaveChildren = collection.level < 2; // Max 3 levels (0, 1, 2)\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().collectionNode),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat((_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().collectionItem), \" \").concat(isSelected ? (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().selected) : ''),\n                    style: {\n                        paddingLeft: \"\".concat(depth * 20 + 12, \"px\")\n                    },\n                    onClick: ()=>handleSelectCollection(collection),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().collectionContent),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().expandButton),\n                                    children: hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            toggleExpanded(collection._id);\n                                        },\n                                        className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().expandToggle),\n                                        children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 33\n                                        }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 61\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 17\n                                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().expandSpacer)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 17\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().collectionIcon),\n                                    children: hasChildren ? isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 30\n                                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 57\n                                    }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 17\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().collectionInfo),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().collectionTitle),\n                                            children: collection.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().collectionMeta),\n                                            children: [\n                                                \"Level \",\n                                                collection.level,\n                                                \" • \",\n                                                collection.handle,\n                                                collection.productCount !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        \" • \",\n                                                        collection.productCount,\n                                                        \" products\"\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().collectionStatus),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"\".concat((_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().statusBadge), \" \").concat(collection.published ? (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().published) : (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().draft)),\n                                        children: collection.published ? 'Published' : 'Draft'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().collectionActions),\n                            children: [\n                                canHaveChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>handleAddSubCollection(e, collection),\n                                    className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                    title: \"Add Sub Collection\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 17\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>handleEditCollection(e, collection),\n                                    className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                    title: \"Edit Collection\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, _this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>handleDeleteCollection(e, collection),\n                                    className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                    title: \"Delete Collection\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        size: 14\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, _this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, _this),\n                hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().childrenContainer),\n                    children: collection.children.map((child)=>renderCollectionNode(child, depth + 1))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, collection._id, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, _this);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().loadingContainer),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().spinner)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading collection hierarchy...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n            lineNumber: 193,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().errorContainer),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().errorMessage),\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: fetchCollectionHierarchy,\n                    className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().retryButton),\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n            lineNumber: 202,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().hierarchyContainer),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().hierarchyHeader),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Collection Hierarchy\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onAddSubCollection === null || onAddSubCollection === void 0 ? void 0 : onAddSubCollection(null),\n                        className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().addRootButton),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            \"Add Root Collection\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().hierarchyTree),\n                children: collections.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().emptyState),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_Edit_Folder_FolderOpen_Package_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            size: 48\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"No collections found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>onAddSubCollection === null || onAddSubCollection === void 0 ? void 0 : onAddSubCollection(null),\n                            className: (_CollectionHierarchy_module_css__WEBPACK_IMPORTED_MODULE_3___default().addFirstButton),\n                            children: \"Create First Collection\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 11\n                }, this) : collections.map((collection)=>renderCollectionNode(collection))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n                lineNumber: 224,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionHierarchy.tsx\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, this);\n}\n_s(CollectionHierarchy, \"iIAkeb7/Nrav3AfNJXYxpqYDE4A=\");\n_c = CollectionHierarchy;\nvar _c;\n$RefreshReg$(_c, \"CollectionHierarchy\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/CollectionHierarchy.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/admin/CollectionModal.module.css":
/*!*********************************************************!*\
  !*** ./src/components/admin/CollectionModal.module.css ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"modalOverlay\":\"CollectionModal_modalOverlay___vawn\",\"modalContainer\":\"CollectionModal_modalContainer__4GZZw\",\"modalHeader\":\"CollectionModal_modalHeader__24erq\",\"closeButton\":\"CollectionModal_closeButton__10tQk\",\"modalForm\":\"CollectionModal_modalForm__1KZOQ\",\"formGrid\":\"CollectionModal_formGrid__3mmYQ\",\"formSection\":\"CollectionModal_formSection__We35p\",\"formGroup\":\"CollectionModal_formGroup__Joi79\",\"inputError\":\"CollectionModal_inputError__NVeC6\",\"errorText\":\"CollectionModal_errorText__4rreW\",\"checkboxLabel\":\"CollectionModal_checkboxLabel__ENzz8\",\"uploadArea\":\"CollectionModal_uploadArea__XG9aV\",\"submitError\":\"CollectionModal_submitError__Gco32\",\"modalActions\":\"CollectionModal_modalActions__qPhNr\",\"cancelButton\":\"CollectionModal_cancelButton__GjBkU\",\"saveButton\":\"CollectionModal_saveButton__4Tx_q\"};\n    if(true) {\n      // 1751482170448\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"2401d8f8c102\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/CollectionModal.module.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/admin/CollectionModal.tsx":
/*!**************************************************!*\
  !*** ./src/components/admin/CollectionModal.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CollectionModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,X!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CollectionModal.module.css */ \"(app-pages-browser)/./src/components/admin/CollectionModal.module.css\");\n/* harmony import */ var _CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction CollectionModal(param) {\n    let { isOpen, onClose, onSave, collection, parentCollection, mode } = param;\n    var _formData_seo, _formData_seo1, _formData_seo2, _formData_seo3, _formData_seo4;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        description: '',\n        collectionType: 'manual',\n        published: false,\n        seo: {\n            title: '',\n            description: '',\n            slug: ''\n        },\n        image: {\n            url: '',\n            altText: ''\n        }\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [availableParents, setAvailableParents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CollectionModal.useEffect\": ()=>{\n            if (isOpen) {\n                if (mode === 'edit' && collection) {\n                    setFormData({\n                        ...collection,\n                        seo: collection.seo || {\n                            title: '',\n                            description: '',\n                            slug: ''\n                        },\n                        image: collection.image || {\n                            url: '',\n                            altText: ''\n                        }\n                    });\n                } else if (mode === 'create') {\n                    setFormData({\n                        title: '',\n                        description: '',\n                        collectionType: 'manual',\n                        published: false,\n                        parent: (parentCollection === null || parentCollection === void 0 ? void 0 : parentCollection._id) || '',\n                        seo: {\n                            title: '',\n                            description: '',\n                            slug: ''\n                        },\n                        image: {\n                            url: '',\n                            altText: ''\n                        }\n                    });\n                }\n                fetchAvailableParents();\n            }\n        }\n    }[\"CollectionModal.useEffect\"], [\n        isOpen,\n        mode,\n        collection,\n        parentCollection\n    ]);\n    const fetchAvailableParents = async ()=>{\n        try {\n            var _response_collections;\n            // Get collections that can be parents (level 0 and 1 only)\n            const response = await _services_api__WEBPACK_IMPORTED_MODULE_2__.collectionsApi.getCollections({\n                limit: 100,\n                published: true\n            });\n            const validParents = ((_response_collections = response.collections) === null || _response_collections === void 0 ? void 0 : _response_collections.filter((c)=>c.level !== undefined && c.level < 2 && c._id !== (collection === null || collection === void 0 ? void 0 : collection._id))) || [];\n            setAvailableParents(validParents);\n        } catch (error) {\n            console.error('Failed to fetch available parents:', error);\n        }\n    };\n    const generateHandle = (title)=>{\n        return title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>{\n            const updated = {\n                ...prev,\n                [field]: value\n            };\n            // Auto-generate handle and SEO slug from title\n            if (field === 'title') {\n                const handle = generateHandle(value);\n                updated.handle = handle;\n                if (updated.seo) {\n                    updated.seo.slug = handle;\n                    if (!updated.seo.title) {\n                        updated.seo.title = value;\n                    }\n                }\n            }\n            return updated;\n        });\n        // Clear error for this field\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: ''\n                }));\n        }\n    };\n    const handleNestedInputChange = (parent, field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [parent]: {\n                    ...prev[parent],\n                    [field]: value\n                }\n            }));\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.title.trim()) {\n            newErrors.title = 'Title is required';\n        }\n        if (!formData.description.trim()) {\n            newErrors.description = 'Description is required';\n        }\n        if (formData.parent) {\n            const parent = availableParents.find((p)=>p._id === formData.parent);\n            if (parent && parent.level !== undefined && parent.level >= 2) {\n                newErrors.parent = 'Cannot add sub-collection to this level';\n            }\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsLoading(true);\n        try {\n            await onSave(formData);\n            onClose();\n        } catch (error) {\n            console.error('Failed to save collection:', error);\n            setErrors({\n                submit: 'Failed to save collection. Please try again.'\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (!isOpen) return null;\n    const modalTitle = mode === 'create' ? parentCollection ? 'Add Sub-Collection to \"'.concat(parentCollection.title, '\"') : 'Create New Collection' : 'Edit \"'.concat(collection === null || collection === void 0 ? void 0 : collection.title, '\"');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalOverlay),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalContainer),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalHeader),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: modalTitle\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().closeButton),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalForm),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().formGrid),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().formSection),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"Basic Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().formGroup),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"title\",\n                                                    children: \"Title *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"title\",\n                                                    type: \"text\",\n                                                    value: formData.title,\n                                                    onChange: (e)=>handleInputChange('title', e.target.value),\n                                                    className: errors.title ? (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().inputError) : '',\n                                                    placeholder: \"Enter collection title\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().errorText),\n                                                    children: errors.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 34\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().formGroup),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"handle\",\n                                                    children: \"Handle\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"handle\",\n                                                    type: \"text\",\n                                                    value: formData.handle || '',\n                                                    onChange: (e)=>handleInputChange('handle', e.target.value),\n                                                    placeholder: \"auto-generated-from-title\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                    children: \"URL-friendly identifier (auto-generated from title)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().formGroup),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"description\",\n                                                    children: \"Description *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"description\",\n                                                    value: formData.description,\n                                                    onChange: (e)=>handleInputChange('description', e.target.value),\n                                                    className: errors.description ? (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().inputError) : '',\n                                                    placeholder: \"Describe this collection\",\n                                                    rows: 4\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().errorText),\n                                                    children: errors.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 40\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        mode === 'create' && availableParents.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().formGroup),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"parent\",\n                                                    children: \"Parent Collection\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"parent\",\n                                                    value: formData.parent || '',\n                                                    onChange: (e)=>handleInputChange('parent', e.target.value),\n                                                    className: errors.parent ? (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().inputError) : '',\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Root Collection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        availableParents.map((parent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: parent._id,\n                                                                children: [\n                                                                    '  '.repeat(parent.level || 0),\n                                                                    parent.title,\n                                                                    \" (Level \",\n                                                                    parent.level,\n                                                                    \")\"\n                                                                ]\n                                                            }, parent._id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this),\n                                                errors.parent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().errorText),\n                                                    children: errors.parent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().formGroup),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"collectionType\",\n                                                    children: \"Collection Type\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"collectionType\",\n                                                    value: formData.collectionType,\n                                                    onChange: (e)=>handleInputChange('collectionType', e.target.value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"manual\",\n                                                            children: \"Manual (Add products manually)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                            lineNumber: 276,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"smart\",\n                                                            children: \"Smart (Auto-populate based on rules)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().formGroup),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().checkboxLabel),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: formData.published,\n                                                            onChange: (e)=>handleInputChange('published', e.target.checked)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Published\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                            lineNumber: 288,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                    children: \"Published collections are visible to customers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().formSection),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"SEO Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().formGroup),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"seoTitle\",\n                                                    children: \"SEO Title\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"seoTitle\",\n                                                    type: \"text\",\n                                                    value: ((_formData_seo = formData.seo) === null || _formData_seo === void 0 ? void 0 : _formData_seo.title) || '',\n                                                    onChange: (e)=>handleNestedInputChange('seo', 'title', e.target.value),\n                                                    placeholder: \"SEO title for search engines\",\n                                                    maxLength: 70\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                    children: [\n                                                        (((_formData_seo1 = formData.seo) === null || _formData_seo1 === void 0 ? void 0 : _formData_seo1.title) || '').length,\n                                                        \"/70 characters\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().formGroup),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"seoDescription\",\n                                                    children: \"SEO Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"seoDescription\",\n                                                    value: ((_formData_seo2 = formData.seo) === null || _formData_seo2 === void 0 ? void 0 : _formData_seo2.description) || '',\n                                                    onChange: (e)=>handleNestedInputChange('seo', 'description', e.target.value),\n                                                    placeholder: \"SEO description for search engines\",\n                                                    maxLength: 160,\n                                                    rows: 3\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                    children: [\n                                                        (((_formData_seo3 = formData.seo) === null || _formData_seo3 === void 0 ? void 0 : _formData_seo3.description) || '').length,\n                                                        \"/160 characters\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().formGroup),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"seoSlug\",\n                                                    children: \"URL Slug\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    id: \"seoSlug\",\n                                                    type: \"text\",\n                                                    value: ((_formData_seo4 = formData.seo) === null || _formData_seo4 === void 0 ? void 0 : _formData_seo4.slug) || '',\n                                                    onChange: (e)=>handleNestedInputChange('seo', 'slug', e.target.value),\n                                                    placeholder: \"url-slug-for-collection\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this),\n                        errors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().submitError),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this),\n                                errors.submit\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalActions),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().cancelButton),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: (_CollectionModal_module_css__WEBPACK_IMPORTED_MODULE_3___default().saveButton),\n                                    children: isLoading ? 'Saving...' : mode === 'create' ? 'Create Collection' : 'Save Changes'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n            lineNumber: 196,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\admin\\\\CollectionModal.tsx\",\n        lineNumber: 195,\n        columnNumber: 5\n    }, this);\n}\n_s(CollectionModal, \"g3U1ndB0HBxLDI7+KztOgsR4Yv8=\");\n_c = CollectionModal;\nvar _c;\n$RefreshReg$(_c, \"CollectionModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FkbWluL0NvbGxlY3Rpb25Nb2RhbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFDVTtBQUNGO0FBQ0Y7QUErQm5DLFNBQVNNLGdCQUFnQixLQU9qQjtRQVBpQixFQUN0Q0MsTUFBTSxFQUNOQyxPQUFPLEVBQ1BDLE1BQU0sRUFDTkMsVUFBVSxFQUNWQyxnQkFBZ0IsRUFDaEJDLElBQUksRUFDaUIsR0FQaUI7UUEwUWZDLGVBS0FBLGdCQU9BQSxnQkFNQUEsZ0JBUUFBOztJQTVSdkIsTUFBTSxDQUFDQSxVQUFVQyxZQUFZLEdBQUdkLCtDQUFRQSxDQUFhO1FBQ25EZSxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsZ0JBQWdCO1FBQ2hCQyxXQUFXO1FBQ1hDLEtBQUs7WUFDSEosT0FBTztZQUNQQyxhQUFhO1lBQ2JJLE1BQU07UUFDUjtRQUNBQyxPQUFPO1lBQ0xDLEtBQUs7WUFDTEMsU0FBUztRQUNYO0lBQ0Y7SUFDQSxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBR3pCLCtDQUFRQSxDQUF5QixDQUFDO0lBQzlELE1BQU0sQ0FBQzBCLFdBQVdDLGFBQWEsR0FBRzNCLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQzRCLGtCQUFrQkMsb0JBQW9CLEdBQUc3QiwrQ0FBUUEsQ0FBZSxFQUFFO0lBRXpFQyxnREFBU0E7cUNBQUM7WUFDUixJQUFJTSxRQUFRO2dCQUNWLElBQUlLLFNBQVMsVUFBVUYsWUFBWTtvQkFDakNJLFlBQVk7d0JBQ1YsR0FBR0osVUFBVTt3QkFDYlMsS0FBS1QsV0FBV1MsR0FBRyxJQUFJOzRCQUFFSixPQUFPOzRCQUFJQyxhQUFhOzRCQUFJSSxNQUFNO3dCQUFHO3dCQUM5REMsT0FBT1gsV0FBV1csS0FBSyxJQUFJOzRCQUFFQyxLQUFLOzRCQUFJQyxTQUFTO3dCQUFHO29CQUNwRDtnQkFDRixPQUFPLElBQUlYLFNBQVMsVUFBVTtvQkFDNUJFLFlBQVk7d0JBQ1ZDLE9BQU87d0JBQ1BDLGFBQWE7d0JBQ2JDLGdCQUFnQjt3QkFDaEJDLFdBQVc7d0JBQ1hZLFFBQVFuQixDQUFBQSw2QkFBQUEsdUNBQUFBLGlCQUFrQm9CLEdBQUcsS0FBSTt3QkFDakNaLEtBQUs7NEJBQUVKLE9BQU87NEJBQUlDLGFBQWE7NEJBQUlJLE1BQU07d0JBQUc7d0JBQzVDQyxPQUFPOzRCQUFFQyxLQUFLOzRCQUFJQyxTQUFTO3dCQUFHO29CQUNoQztnQkFDRjtnQkFDQVM7WUFDRjtRQUNGO29DQUFHO1FBQUN6QjtRQUFRSztRQUFNRjtRQUFZQztLQUFpQjtJQUUvQyxNQUFNcUIsd0JBQXdCO1FBQzVCLElBQUk7Z0JBT21CQztZQU5yQiwyREFBMkQ7WUFDM0QsTUFBTUEsV0FBVyxNQUFNN0IseURBQWNBLENBQUM4QixjQUFjLENBQUM7Z0JBQ25EQyxPQUFPO2dCQUNQakIsV0FBVztZQUNiO1lBRUEsTUFBTWtCLGVBQWVILEVBQUFBLHdCQUFBQSxTQUFTSSxXQUFXLGNBQXBCSiw0Q0FBQUEsc0JBQXNCSyxNQUFNLENBQUMsQ0FBQ0MsSUFDakRBLEVBQUVDLEtBQUssS0FBS0MsYUFBYUYsRUFBRUMsS0FBSyxHQUFHLEtBQUtELEVBQUVSLEdBQUcsTUFBS3JCLHVCQUFBQSxpQ0FBQUEsV0FBWXFCLEdBQUcsT0FDOUQsRUFBRTtZQUVQRixvQkFBb0JPO1FBQ3RCLEVBQUUsT0FBT00sT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsc0NBQXNDQTtRQUN0RDtJQUNGO0lBRUEsTUFBTUUsaUJBQWlCLENBQUM3QjtRQUN0QixPQUFPQSxNQUNKOEIsV0FBVyxHQUNYQyxPQUFPLENBQUMsZUFBZSxLQUN2QkEsT0FBTyxDQUFDLFlBQVk7SUFDekI7SUFFQSxNQUFNQyxvQkFBb0IsQ0FBQ0MsT0FBZUM7UUFDeENuQyxZQUFZb0MsQ0FBQUE7WUFDVixNQUFNQyxVQUFVO2dCQUFFLEdBQUdELElBQUk7Z0JBQUUsQ0FBQ0YsTUFBTSxFQUFFQztZQUFNO1lBRTFDLCtDQUErQztZQUMvQyxJQUFJRCxVQUFVLFNBQVM7Z0JBQ3JCLE1BQU1JLFNBQVNSLGVBQWVLO2dCQUM5QkUsUUFBUUMsTUFBTSxHQUFHQTtnQkFDakIsSUFBSUQsUUFBUWhDLEdBQUcsRUFBRTtvQkFDZmdDLFFBQVFoQyxHQUFHLENBQUNDLElBQUksR0FBR2dDO29CQUNuQixJQUFJLENBQUNELFFBQVFoQyxHQUFHLENBQUNKLEtBQUssRUFBRTt3QkFDdEJvQyxRQUFRaEMsR0FBRyxDQUFDSixLQUFLLEdBQUdrQztvQkFDdEI7Z0JBQ0Y7WUFDRjtZQUVBLE9BQU9FO1FBQ1Q7UUFFQSw2QkFBNkI7UUFDN0IsSUFBSTNCLE1BQU0sQ0FBQ3dCLE1BQU0sRUFBRTtZQUNqQnZCLFVBQVV5QixDQUFBQSxPQUFTO29CQUFFLEdBQUdBLElBQUk7b0JBQUUsQ0FBQ0YsTUFBTSxFQUFFO2dCQUFHO1FBQzVDO0lBQ0Y7SUFFQSxNQUFNSywwQkFBMEIsQ0FBQ3ZCLFFBQWdCa0IsT0FBZUM7UUFDOURuQyxZQUFZb0MsQ0FBQUEsT0FBUztnQkFDbkIsR0FBR0EsSUFBSTtnQkFDUCxDQUFDcEIsT0FBTyxFQUFFO29CQUNSLEdBQUdvQixJQUFJLENBQUNwQixPQUEyQjtvQkFDbkMsQ0FBQ2tCLE1BQU0sRUFBRUM7Z0JBQ1g7WUFDRjtJQUNGO0lBRUEsTUFBTUssZUFBZTtRQUNuQixNQUFNQyxZQUFvQyxDQUFDO1FBRTNDLElBQUksQ0FBQzFDLFNBQVNFLEtBQUssQ0FBQ3lDLElBQUksSUFBSTtZQUMxQkQsVUFBVXhDLEtBQUssR0FBRztRQUNwQjtRQUVBLElBQUksQ0FBQ0YsU0FBU0csV0FBVyxDQUFDd0MsSUFBSSxJQUFJO1lBQ2hDRCxVQUFVdkMsV0FBVyxHQUFHO1FBQzFCO1FBRUEsSUFBSUgsU0FBU2lCLE1BQU0sRUFBRTtZQUNuQixNQUFNQSxTQUFTRixpQkFBaUI2QixJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUUzQixHQUFHLEtBQUtsQixTQUFTaUIsTUFBTTtZQUNuRSxJQUFJQSxVQUFVQSxPQUFPVSxLQUFLLEtBQUtDLGFBQWFYLE9BQU9VLEtBQUssSUFBSSxHQUFHO2dCQUM3RGUsVUFBVXpCLE1BQU0sR0FBRztZQUNyQjtRQUNGO1FBRUFMLFVBQVU4QjtRQUNWLE9BQU9JLE9BQU9DLElBQUksQ0FBQ0wsV0FBV00sTUFBTSxLQUFLO0lBQzNDO0lBRUEsTUFBTUMsZUFBZSxPQUFPQztRQUMxQkEsRUFBRUMsY0FBYztRQUVoQixJQUFJLENBQUNWLGdCQUFnQjtZQUNuQjtRQUNGO1FBRUEzQixhQUFhO1FBQ2IsSUFBSTtZQUNGLE1BQU1sQixPQUFPSTtZQUNiTDtRQUNGLEVBQUUsT0FBT2tDLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUNqQixVQUFVO2dCQUFFd0MsUUFBUTtZQUErQztRQUNyRSxTQUFVO1lBQ1J0QyxhQUFhO1FBQ2Y7SUFDRjtJQUVBLElBQUksQ0FBQ3BCLFFBQVEsT0FBTztJQUVwQixNQUFNMkQsYUFBYXRELFNBQVMsV0FDdkJELG1CQUFtQiwwQkFBaUQsT0FBdkJBLGlCQUFpQkksS0FBSyxFQUFDLE9BQUssMEJBQzFFLFNBQTJCLE9BQWxCTCx1QkFBQUEsaUNBQUFBLFdBQVlLLEtBQUssRUFBQztJQUUvQixxQkFDRSw4REFBQ29EO1FBQUlDLFdBQVcvRCxpRkFBbUI7a0JBQ2pDLDRFQUFDOEQ7WUFBSUMsV0FBVy9ELG1GQUFxQjs7OEJBQ25DLDhEQUFDOEQ7b0JBQUlDLFdBQVcvRCxnRkFBa0I7O3NDQUNoQyw4REFBQ21FO3NDQUFJTjs7Ozs7O3NDQUNMLDhEQUFDTzs0QkFBT0MsU0FBU2xFOzRCQUFTNEQsV0FBVy9ELGdGQUFrQjtzQ0FDckQsNEVBQUNILHlGQUFDQTtnQ0FBQzBFLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQUliLDhEQUFDQztvQkFBS0MsVUFBVWhCO29CQUFjTSxXQUFXL0QsOEVBQWdCOztzQ0FDdkQsOERBQUM4RDs0QkFBSUMsV0FBVy9ELDZFQUFlOzs4Q0FFN0IsOERBQUM4RDtvQ0FBSUMsV0FBVy9ELGdGQUFrQjs7c0RBQ2hDLDhEQUFDNkU7c0RBQUc7Ozs7OztzREFFSiw4REFBQ2Y7NENBQUlDLFdBQVcvRCw4RUFBZ0I7OzhEQUM5Qiw4REFBQytFO29EQUFNQyxTQUFROzhEQUFROzs7Ozs7OERBQ3ZCLDhEQUFDQztvREFDQ0MsSUFBRztvREFDSEMsTUFBSztvREFDTHZDLE9BQU9wQyxTQUFTRSxLQUFLO29EQUNyQjBFLFVBQVUsQ0FBQzFCLElBQU1oQixrQkFBa0IsU0FBU2dCLEVBQUUyQixNQUFNLENBQUN6QyxLQUFLO29EQUMxRG1CLFdBQVc1QyxPQUFPVCxLQUFLLEdBQUdWLCtFQUFpQixHQUFHO29EQUM5Q3VGLGFBQVk7Ozs7OztnREFFYnBFLE9BQU9ULEtBQUssa0JBQUksOERBQUM4RTtvREFBS3pCLFdBQVcvRCw4RUFBZ0I7OERBQUdtQixPQUFPVCxLQUFLOzs7Ozs7Ozs7Ozs7c0RBR25FLDhEQUFDb0Q7NENBQUlDLFdBQVcvRCw4RUFBZ0I7OzhEQUM5Qiw4REFBQytFO29EQUFNQyxTQUFROzhEQUFTOzs7Ozs7OERBQ3hCLDhEQUFDQztvREFDQ0MsSUFBRztvREFDSEMsTUFBSztvREFDTHZDLE9BQU9wQyxTQUFTdUMsTUFBTSxJQUFJO29EQUMxQnFDLFVBQVUsQ0FBQzFCLElBQU1oQixrQkFBa0IsVUFBVWdCLEVBQUUyQixNQUFNLENBQUN6QyxLQUFLO29EQUMzRDJDLGFBQVk7Ozs7Ozs4REFFZCw4REFBQ0c7OERBQU07Ozs7Ozs7Ozs7OztzREFHVCw4REFBQzVCOzRDQUFJQyxXQUFXL0QsOEVBQWdCOzs4REFDOUIsOERBQUMrRTtvREFBTUMsU0FBUTs4REFBYzs7Ozs7OzhEQUM3Qiw4REFBQ1c7b0RBQ0NULElBQUc7b0RBQ0h0QyxPQUFPcEMsU0FBU0csV0FBVztvREFDM0J5RSxVQUFVLENBQUMxQixJQUFNaEIsa0JBQWtCLGVBQWVnQixFQUFFMkIsTUFBTSxDQUFDekMsS0FBSztvREFDaEVtQixXQUFXNUMsT0FBT1IsV0FBVyxHQUFHWCwrRUFBaUIsR0FBRztvREFDcER1RixhQUFZO29EQUNaSyxNQUFNOzs7Ozs7Z0RBRVB6RSxPQUFPUixXQUFXLGtCQUFJLDhEQUFDNkU7b0RBQUt6QixXQUFXL0QsOEVBQWdCOzhEQUFHbUIsT0FBT1IsV0FBVzs7Ozs7Ozs7Ozs7O3dDQUk5RUosU0FBUyxZQUFZZ0IsaUJBQWlCaUMsTUFBTSxHQUFHLG1CQUM5Qyw4REFBQ007NENBQUlDLFdBQVcvRCw4RUFBZ0I7OzhEQUM5Qiw4REFBQytFO29EQUFNQyxTQUFROzhEQUFTOzs7Ozs7OERBQ3hCLDhEQUFDYTtvREFDQ1gsSUFBRztvREFDSHRDLE9BQU9wQyxTQUFTaUIsTUFBTSxJQUFJO29EQUMxQjJELFVBQVUsQ0FBQzFCLElBQU1oQixrQkFBa0IsVUFBVWdCLEVBQUUyQixNQUFNLENBQUN6QyxLQUFLO29EQUMzRG1CLFdBQVc1QyxPQUFPTSxNQUFNLEdBQUd6QiwrRUFBaUIsR0FBRzs7c0VBRS9DLDhEQUFDOEY7NERBQU9sRCxPQUFNO3NFQUFHOzs7Ozs7d0RBQ2hCckIsaUJBQWlCd0UsR0FBRyxDQUFDLENBQUN0RSx1QkFDckIsOERBQUNxRTtnRUFBd0JsRCxPQUFPbkIsT0FBT0MsR0FBRzs7b0VBQ3ZDLEtBQUtzRSxNQUFNLENBQUN2RSxPQUFPVSxLQUFLLElBQUk7b0VBQUlWLE9BQU9mLEtBQUs7b0VBQUM7b0VBQVNlLE9BQU9VLEtBQUs7b0VBQUM7OytEQUR6RFYsT0FBT0MsR0FBRzs7Ozs7Ozs7Ozs7Z0RBSzFCUCxPQUFPTSxNQUFNLGtCQUFJLDhEQUFDK0Q7b0RBQUt6QixXQUFXL0QsOEVBQWdCOzhEQUFHbUIsT0FBT00sTUFBTTs7Ozs7Ozs7Ozs7O3NEQUl2RSw4REFBQ3FDOzRDQUFJQyxXQUFXL0QsOEVBQWdCOzs4REFDOUIsOERBQUMrRTtvREFBTUMsU0FBUTs4REFBaUI7Ozs7Ozs4REFDaEMsOERBQUNhO29EQUNDWCxJQUFHO29EQUNIdEMsT0FBT3BDLFNBQVNJLGNBQWM7b0RBQzlCd0UsVUFBVSxDQUFDMUIsSUFBTWhCLGtCQUFrQixrQkFBa0JnQixFQUFFMkIsTUFBTSxDQUFDekMsS0FBSzs7c0VBRW5FLDhEQUFDa0Q7NERBQU9sRCxPQUFNO3NFQUFTOzs7Ozs7c0VBQ3ZCLDhEQUFDa0Q7NERBQU9sRCxPQUFNO3NFQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBSTFCLDhEQUFDa0I7NENBQUlDLFdBQVcvRCw4RUFBZ0I7OzhEQUM5Qiw4REFBQytFO29EQUFNaEIsV0FBVy9ELGtGQUFvQjs7c0VBQ3BDLDhEQUFDaUY7NERBQ0NFLE1BQUs7NERBQ0xlLFNBQVMxRixTQUFTSyxTQUFTOzREQUMzQnVFLFVBQVUsQ0FBQzFCLElBQU1oQixrQkFBa0IsYUFBYWdCLEVBQUUyQixNQUFNLENBQUNhLE9BQU87Ozs7OztzRUFFbEUsOERBQUNWO3NFQUFLOzs7Ozs7Ozs7Ozs7OERBRVIsOERBQUNFOzhEQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBS1gsOERBQUM1QjtvQ0FBSUMsV0FBVy9ELGdGQUFrQjs7c0RBQ2hDLDhEQUFDNkU7c0RBQUc7Ozs7OztzREFFSiw4REFBQ2Y7NENBQUlDLFdBQVcvRCw4RUFBZ0I7OzhEQUM5Qiw4REFBQytFO29EQUFNQyxTQUFROzhEQUFXOzs7Ozs7OERBQzFCLDhEQUFDQztvREFDQ0MsSUFBRztvREFDSEMsTUFBSztvREFDTHZDLE9BQU9wQyxFQUFBQSxnQkFBQUEsU0FBU00sR0FBRyxjQUFaTixvQ0FBQUEsY0FBY0UsS0FBSyxLQUFJO29EQUM5QjBFLFVBQVUsQ0FBQzFCLElBQU1WLHdCQUF3QixPQUFPLFNBQVNVLEVBQUUyQixNQUFNLENBQUN6QyxLQUFLO29EQUN2RTJDLGFBQVk7b0RBQ1pZLFdBQVc7Ozs7Ozs4REFFYiw4REFBQ1Q7O3dEQUFRbEYsQ0FBQUEsRUFBQUEsaUJBQUFBLFNBQVNNLEdBQUcsY0FBWk4scUNBQUFBLGVBQWNFLEtBQUssS0FBSSxFQUFDLEVBQUc4QyxNQUFNO3dEQUFDOzs7Ozs7Ozs7Ozs7O3NEQUc3Qyw4REFBQ007NENBQUlDLFdBQVcvRCw4RUFBZ0I7OzhEQUM5Qiw4REFBQytFO29EQUFNQyxTQUFROzhEQUFpQjs7Ozs7OzhEQUNoQyw4REFBQ1c7b0RBQ0NULElBQUc7b0RBQ0h0QyxPQUFPcEMsRUFBQUEsaUJBQUFBLFNBQVNNLEdBQUcsY0FBWk4scUNBQUFBLGVBQWNHLFdBQVcsS0FBSTtvREFDcEN5RSxVQUFVLENBQUMxQixJQUFNVix3QkFBd0IsT0FBTyxlQUFlVSxFQUFFMkIsTUFBTSxDQUFDekMsS0FBSztvREFDN0UyQyxhQUFZO29EQUNaWSxXQUFXO29EQUNYUCxNQUFNOzs7Ozs7OERBRVIsOERBQUNGOzt3REFBUWxGLENBQUFBLEVBQUFBLGlCQUFBQSxTQUFTTSxHQUFHLGNBQVpOLHFDQUFBQSxlQUFjRyxXQUFXLEtBQUksRUFBQyxFQUFHNkMsTUFBTTt3REFBQzs7Ozs7Ozs7Ozs7OztzREFHbkQsOERBQUNNOzRDQUFJQyxXQUFXL0QsOEVBQWdCOzs4REFDOUIsOERBQUMrRTtvREFBTUMsU0FBUTs4REFBVTs7Ozs7OzhEQUN6Qiw4REFBQ0M7b0RBQ0NDLElBQUc7b0RBQ0hDLE1BQUs7b0RBQ0x2QyxPQUFPcEMsRUFBQUEsaUJBQUFBLFNBQVNNLEdBQUcsY0FBWk4scUNBQUFBLGVBQWNPLElBQUksS0FBSTtvREFDN0JxRSxVQUFVLENBQUMxQixJQUFNVix3QkFBd0IsT0FBTyxRQUFRVSxFQUFFMkIsTUFBTSxDQUFDekMsS0FBSztvREFDdEUyQyxhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBTW5CcEUsT0FBT3lDLE1BQU0sa0JBQ1osOERBQUNFOzRCQUFJQyxXQUFXL0QsZ0ZBQWtCOzs4Q0FDaEMsOERBQUNGLHlGQUFXQTtvQ0FBQ3lFLE1BQU07Ozs7OztnQ0FDbEJwRCxPQUFPeUMsTUFBTTs7Ozs7OztzQ0FJbEIsOERBQUNFOzRCQUFJQyxXQUFXL0QsaUZBQW1COzs4Q0FDakMsOERBQUNvRTtvQ0FBT2UsTUFBSztvQ0FBU2QsU0FBU2xFO29DQUFTNEQsV0FBVy9ELGlGQUFtQjs4Q0FBRTs7Ozs7OzhDQUd4RSw4REFBQ29FO29DQUFPZSxNQUFLO29DQUFTb0IsVUFBVWxGO29DQUFXMEMsV0FBVy9ELCtFQUFpQjs4Q0FDcEVxQixZQUFZLGNBQWVkLFNBQVMsV0FBVyxzQkFBc0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT3BGO0dBL1R3Qk47S0FBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVW1lciBGYXJvb3FcXERlc2t0b3BcXFBhdHJpY2tzIHdlYlxcQ2FzdC1TdG9uZVxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcYWRtaW5cXENvbGxlY3Rpb25Nb2RhbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgWCwgVXBsb2FkLCBBbGVydENpcmNsZSB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyBjb2xsZWN0aW9uc0FwaSB9IGZyb20gJy4uLy4uL3NlcnZpY2VzL2FwaSc7XG5pbXBvcnQgc3R5bGVzIGZyb20gJy4vQ29sbGVjdGlvbk1vZGFsLm1vZHVsZS5jc3MnO1xuXG5pbnRlcmZhY2UgQ29sbGVjdGlvbiB7XG4gIF9pZD86IHN0cmluZztcbiAgdGl0bGU6IHN0cmluZztcbiAgaGFuZGxlPzogc3RyaW5nO1xuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xuICBwYXJlbnQ/OiBzdHJpbmc7XG4gIGxldmVsPzogbnVtYmVyO1xuICBjb2xsZWN0aW9uVHlwZTogJ21hbnVhbCcgfCAnc21hcnQnO1xuICBwdWJsaXNoZWQ6IGJvb2xlYW47XG4gIHNlbz86IHtcbiAgICB0aXRsZT86IHN0cmluZztcbiAgICBkZXNjcmlwdGlvbj86IHN0cmluZztcbiAgICBzbHVnPzogc3RyaW5nO1xuICB9O1xuICBpbWFnZT86IHtcbiAgICB1cmw/OiBzdHJpbmc7XG4gICAgYWx0VGV4dD86IHN0cmluZztcbiAgfTtcbn1cblxuaW50ZXJmYWNlIENvbGxlY3Rpb25Nb2RhbFByb3BzIHtcbiAgaXNPcGVuOiBib29sZWFuO1xuICBvbkNsb3NlOiAoKSA9PiB2b2lkO1xuICBvblNhdmU6IChjb2xsZWN0aW9uOiBDb2xsZWN0aW9uKSA9PiB2b2lkO1xuICBjb2xsZWN0aW9uPzogQ29sbGVjdGlvbiB8IG51bGw7XG4gIHBhcmVudENvbGxlY3Rpb24/OiBDb2xsZWN0aW9uIHwgbnVsbDtcbiAgbW9kZTogJ2NyZWF0ZScgfCAnZWRpdCc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENvbGxlY3Rpb25Nb2RhbCh7XG4gIGlzT3BlbixcbiAgb25DbG9zZSxcbiAgb25TYXZlLFxuICBjb2xsZWN0aW9uLFxuICBwYXJlbnRDb2xsZWN0aW9uLFxuICBtb2RlXG59OiBDb2xsZWN0aW9uTW9kYWxQcm9wcykge1xuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlPENvbGxlY3Rpb24+KHtcbiAgICB0aXRsZTogJycsXG4gICAgZGVzY3JpcHRpb246ICcnLFxuICAgIGNvbGxlY3Rpb25UeXBlOiAnbWFudWFsJyxcbiAgICBwdWJsaXNoZWQ6IGZhbHNlLFxuICAgIHNlbzoge1xuICAgICAgdGl0bGU6ICcnLFxuICAgICAgZGVzY3JpcHRpb246ICcnLFxuICAgICAgc2x1ZzogJydcbiAgICB9LFxuICAgIGltYWdlOiB7XG4gICAgICB1cmw6ICcnLFxuICAgICAgYWx0VGV4dDogJydcbiAgICB9XG4gIH0pO1xuICBjb25zdCBbZXJyb3JzLCBzZXRFcnJvcnNdID0gdXNlU3RhdGU8UmVjb3JkPHN0cmluZywgc3RyaW5nPj4oe30pO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbYXZhaWxhYmxlUGFyZW50cywgc2V0QXZhaWxhYmxlUGFyZW50c10gPSB1c2VTdGF0ZTxDb2xsZWN0aW9uW10+KFtdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpc09wZW4pIHtcbiAgICAgIGlmIChtb2RlID09PSAnZWRpdCcgJiYgY29sbGVjdGlvbikge1xuICAgICAgICBzZXRGb3JtRGF0YSh7XG4gICAgICAgICAgLi4uY29sbGVjdGlvbixcbiAgICAgICAgICBzZW86IGNvbGxlY3Rpb24uc2VvIHx8IHsgdGl0bGU6ICcnLCBkZXNjcmlwdGlvbjogJycsIHNsdWc6ICcnIH0sXG4gICAgICAgICAgaW1hZ2U6IGNvbGxlY3Rpb24uaW1hZ2UgfHwgeyB1cmw6ICcnLCBhbHRUZXh0OiAnJyB9XG4gICAgICAgIH0pO1xuICAgICAgfSBlbHNlIGlmIChtb2RlID09PSAnY3JlYXRlJykge1xuICAgICAgICBzZXRGb3JtRGF0YSh7XG4gICAgICAgICAgdGl0bGU6ICcnLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiAnJyxcbiAgICAgICAgICBjb2xsZWN0aW9uVHlwZTogJ21hbnVhbCcsXG4gICAgICAgICAgcHVibGlzaGVkOiBmYWxzZSxcbiAgICAgICAgICBwYXJlbnQ6IHBhcmVudENvbGxlY3Rpb24/Ll9pZCB8fCAnJyxcbiAgICAgICAgICBzZW86IHsgdGl0bGU6ICcnLCBkZXNjcmlwdGlvbjogJycsIHNsdWc6ICcnIH0sXG4gICAgICAgICAgaW1hZ2U6IHsgdXJsOiAnJywgYWx0VGV4dDogJycgfVxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICAgIGZldGNoQXZhaWxhYmxlUGFyZW50cygpO1xuICAgIH1cbiAgfSwgW2lzT3BlbiwgbW9kZSwgY29sbGVjdGlvbiwgcGFyZW50Q29sbGVjdGlvbl0pO1xuXG4gIGNvbnN0IGZldGNoQXZhaWxhYmxlUGFyZW50cyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8gR2V0IGNvbGxlY3Rpb25zIHRoYXQgY2FuIGJlIHBhcmVudHMgKGxldmVsIDAgYW5kIDEgb25seSlcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgY29sbGVjdGlvbnNBcGkuZ2V0Q29sbGVjdGlvbnMoeyBcbiAgICAgICAgbGltaXQ6IDEwMCxcbiAgICAgICAgcHVibGlzaGVkOiB0cnVlIFxuICAgICAgfSk7XG4gICAgICBcbiAgICAgIGNvbnN0IHZhbGlkUGFyZW50cyA9IHJlc3BvbnNlLmNvbGxlY3Rpb25zPy5maWx0ZXIoKGM6IENvbGxlY3Rpb24pID0+IFxuICAgICAgICBjLmxldmVsICE9PSB1bmRlZmluZWQgJiYgYy5sZXZlbCA8IDIgJiYgYy5faWQgIT09IGNvbGxlY3Rpb24/Ll9pZFxuICAgICAgKSB8fCBbXTtcbiAgICAgIFxuICAgICAgc2V0QXZhaWxhYmxlUGFyZW50cyh2YWxpZFBhcmVudHMpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggYXZhaWxhYmxlIHBhcmVudHM6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZW5lcmF0ZUhhbmRsZSA9ICh0aXRsZTogc3RyaW5nKSA9PiB7XG4gICAgcmV0dXJuIHRpdGxlXG4gICAgICAudG9Mb3dlckNhc2UoKVxuICAgICAgLnJlcGxhY2UoL1teYS16MC05XSsvZywgJy0nKVxuICAgICAgLnJlcGxhY2UoL14tK3wtKyQvZywgJycpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUlucHV0Q2hhbmdlID0gKGZpZWxkOiBzdHJpbmcsIHZhbHVlOiBhbnkpID0+IHtcbiAgICBzZXRGb3JtRGF0YShwcmV2ID0+IHtcbiAgICAgIGNvbnN0IHVwZGF0ZWQgPSB7IC4uLnByZXYsIFtmaWVsZF06IHZhbHVlIH07XG4gICAgICBcbiAgICAgIC8vIEF1dG8tZ2VuZXJhdGUgaGFuZGxlIGFuZCBTRU8gc2x1ZyBmcm9tIHRpdGxlXG4gICAgICBpZiAoZmllbGQgPT09ICd0aXRsZScpIHtcbiAgICAgICAgY29uc3QgaGFuZGxlID0gZ2VuZXJhdGVIYW5kbGUodmFsdWUpO1xuICAgICAgICB1cGRhdGVkLmhhbmRsZSA9IGhhbmRsZTtcbiAgICAgICAgaWYgKHVwZGF0ZWQuc2VvKSB7XG4gICAgICAgICAgdXBkYXRlZC5zZW8uc2x1ZyA9IGhhbmRsZTtcbiAgICAgICAgICBpZiAoIXVwZGF0ZWQuc2VvLnRpdGxlKSB7XG4gICAgICAgICAgICB1cGRhdGVkLnNlby50aXRsZSA9IHZhbHVlO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgXG4gICAgICByZXR1cm4gdXBkYXRlZDtcbiAgICB9KTtcbiAgICBcbiAgICAvLyBDbGVhciBlcnJvciBmb3IgdGhpcyBmaWVsZFxuICAgIGlmIChlcnJvcnNbZmllbGRdKSB7XG4gICAgICBzZXRFcnJvcnMocHJldiA9PiAoeyAuLi5wcmV2LCBbZmllbGRdOiAnJyB9KSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZU5lc3RlZElucHV0Q2hhbmdlID0gKHBhcmVudDogc3RyaW5nLCBmaWVsZDogc3RyaW5nLCB2YWx1ZTogYW55KSA9PiB7XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIFtwYXJlbnRdOiB7XG4gICAgICAgIC4uLnByZXZbcGFyZW50IGFzIGtleW9mIENvbGxlY3Rpb25dLFxuICAgICAgICBbZmllbGRdOiB2YWx1ZVxuICAgICAgfVxuICAgIH0pKTtcbiAgfTtcblxuICBjb25zdCB2YWxpZGF0ZUZvcm0gPSAoKSA9PiB7XG4gICAgY29uc3QgbmV3RXJyb3JzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge307XG5cbiAgICBpZiAoIWZvcm1EYXRhLnRpdGxlLnRyaW0oKSkge1xuICAgICAgbmV3RXJyb3JzLnRpdGxlID0gJ1RpdGxlIGlzIHJlcXVpcmVkJztcbiAgICB9XG5cbiAgICBpZiAoIWZvcm1EYXRhLmRlc2NyaXB0aW9uLnRyaW0oKSkge1xuICAgICAgbmV3RXJyb3JzLmRlc2NyaXB0aW9uID0gJ0Rlc2NyaXB0aW9uIGlzIHJlcXVpcmVkJztcbiAgICB9XG5cbiAgICBpZiAoZm9ybURhdGEucGFyZW50KSB7XG4gICAgICBjb25zdCBwYXJlbnQgPSBhdmFpbGFibGVQYXJlbnRzLmZpbmQocCA9PiBwLl9pZCA9PT0gZm9ybURhdGEucGFyZW50KTtcbiAgICAgIGlmIChwYXJlbnQgJiYgcGFyZW50LmxldmVsICE9PSB1bmRlZmluZWQgJiYgcGFyZW50LmxldmVsID49IDIpIHtcbiAgICAgICAgbmV3RXJyb3JzLnBhcmVudCA9ICdDYW5ub3QgYWRkIHN1Yi1jb2xsZWN0aW9uIHRvIHRoaXMgbGV2ZWwnO1xuICAgICAgfVxuICAgIH1cblxuICAgIHNldEVycm9ycyhuZXdFcnJvcnMpO1xuICAgIHJldHVybiBPYmplY3Qua2V5cyhuZXdFcnJvcnMpLmxlbmd0aCA9PT0gMDtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIFxuICAgIGlmICghdmFsaWRhdGVGb3JtKCkpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IG9uU2F2ZShmb3JtRGF0YSk7XG4gICAgICBvbkNsb3NlKCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBzYXZlIGNvbGxlY3Rpb246JywgZXJyb3IpO1xuICAgICAgc2V0RXJyb3JzKHsgc3VibWl0OiAnRmFpbGVkIHRvIHNhdmUgY29sbGVjdGlvbi4gUGxlYXNlIHRyeSBhZ2Fpbi4nIH0pO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBpZiAoIWlzT3BlbikgcmV0dXJuIG51bGw7XG5cbiAgY29uc3QgbW9kYWxUaXRsZSA9IG1vZGUgPT09ICdjcmVhdGUnIFxuICAgID8gKHBhcmVudENvbGxlY3Rpb24gPyBgQWRkIFN1Yi1Db2xsZWN0aW9uIHRvIFwiJHtwYXJlbnRDb2xsZWN0aW9uLnRpdGxlfVwiYCA6ICdDcmVhdGUgTmV3IENvbGxlY3Rpb24nKVxuICAgIDogYEVkaXQgXCIke2NvbGxlY3Rpb24/LnRpdGxlfVwiYDtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubW9kYWxPdmVybGF5fT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubW9kYWxDb250YWluZXJ9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm1vZGFsSGVhZGVyfT5cbiAgICAgICAgICA8aDI+e21vZGFsVGl0bGV9PC9oMj5cbiAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e29uQ2xvc2V9IGNsYXNzTmFtZT17c3R5bGVzLmNsb3NlQnV0dG9ufT5cbiAgICAgICAgICAgIDxYIHNpemU9ezIwfSAvPlxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9e3N0eWxlcy5tb2RhbEZvcm19PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuZm9ybUdyaWR9PlxuICAgICAgICAgICAgey8qIEJhc2ljIEluZm9ybWF0aW9uICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5mb3JtU2VjdGlvbn0+XG4gICAgICAgICAgICAgIDxoMz5CYXNpYyBJbmZvcm1hdGlvbjwvaDM+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmZvcm1Hcm91cH0+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJ0aXRsZVwiPlRpdGxlICo8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgaWQ9XCJ0aXRsZVwiXG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudGl0bGV9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCd0aXRsZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17ZXJyb3JzLnRpdGxlID8gc3R5bGVzLmlucHV0RXJyb3IgOiAnJ31cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW50ZXIgY29sbGVjdGlvbiB0aXRsZVwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICB7ZXJyb3JzLnRpdGxlICYmIDxzcGFuIGNsYXNzTmFtZT17c3R5bGVzLmVycm9yVGV4dH0+e2Vycm9ycy50aXRsZX08L3NwYW4+fVxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmZvcm1Hcm91cH0+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJoYW5kbGVcIj5IYW5kbGU8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgaWQ9XCJoYW5kbGVcIlxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmhhbmRsZSB8fCAnJ31cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ2hhbmRsZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiYXV0by1nZW5lcmF0ZWQtZnJvbS10aXRsZVwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8c21hbGw+VVJMLWZyaWVuZGx5IGlkZW50aWZpZXIgKGF1dG8tZ2VuZXJhdGVkIGZyb20gdGl0bGUpPC9zbWFsbD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5mb3JtR3JvdXB9PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBodG1sRm9yPVwiZGVzY3JpcHRpb25cIj5EZXNjcmlwdGlvbiAqPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgIGlkPVwiZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZSgnZGVzY3JpcHRpb24nLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Vycm9ycy5kZXNjcmlwdGlvbiA/IHN0eWxlcy5pbnB1dEVycm9yIDogJyd9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkRlc2NyaWJlIHRoaXMgY29sbGVjdGlvblwiXG4gICAgICAgICAgICAgICAgICByb3dzPXs0fVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAge2Vycm9ycy5kZXNjcmlwdGlvbiAmJiA8c3BhbiBjbGFzc05hbWU9e3N0eWxlcy5lcnJvclRleHR9PntlcnJvcnMuZGVzY3JpcHRpb259PC9zcGFuPn1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIFBhcmVudCBDb2xsZWN0aW9uIFNlbGVjdGlvbiAqL31cbiAgICAgICAgICAgICAge21vZGUgPT09ICdjcmVhdGUnICYmIGF2YWlsYWJsZVBhcmVudHMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5mb3JtR3JvdXB9PlxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJwYXJlbnRcIj5QYXJlbnQgQ29sbGVjdGlvbjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgIGlkPVwicGFyZW50XCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBhcmVudCB8fCAnJ31cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZSgncGFyZW50JywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Vycm9ycy5wYXJlbnQgPyBzdHlsZXMuaW5wdXRFcnJvciA6ICcnfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+Um9vdCBDb2xsZWN0aW9uPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIHthdmFpbGFibGVQYXJlbnRzLm1hcCgocGFyZW50KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e3BhcmVudC5faWR9IHZhbHVlPXtwYXJlbnQuX2lkfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsnICAnLnJlcGVhdChwYXJlbnQubGV2ZWwgfHwgMCl9e3BhcmVudC50aXRsZX0gKExldmVsIHtwYXJlbnQubGV2ZWx9KVxuICAgICAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAge2Vycm9ycy5wYXJlbnQgJiYgPHNwYW4gY2xhc3NOYW1lPXtzdHlsZXMuZXJyb3JUZXh0fT57ZXJyb3JzLnBhcmVudH08L3NwYW4+fVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuZm9ybUdyb3VwfT5cbiAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cImNvbGxlY3Rpb25UeXBlXCI+Q29sbGVjdGlvbiBUeXBlPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICBpZD1cImNvbGxlY3Rpb25UeXBlXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jb2xsZWN0aW9uVHlwZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ2NvbGxlY3Rpb25UeXBlJywgZS50YXJnZXQudmFsdWUgYXMgJ21hbnVhbCcgfCAnc21hcnQnKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwibWFudWFsXCI+TWFudWFsIChBZGQgcHJvZHVjdHMgbWFudWFsbHkpPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwic21hcnRcIj5TbWFydCAoQXV0by1wb3B1bGF0ZSBiYXNlZCBvbiBydWxlcyk8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5mb3JtR3JvdXB9PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9e3N0eWxlcy5jaGVja2JveExhYmVsfT5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtmb3JtRGF0YS5wdWJsaXNoZWR9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ3B1Ymxpc2hlZCcsIGUudGFyZ2V0LmNoZWNrZWQpfVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuPlB1Ymxpc2hlZDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxzbWFsbD5QdWJsaXNoZWQgY29sbGVjdGlvbnMgYXJlIHZpc2libGUgdG8gY3VzdG9tZXJzPC9zbWFsbD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFNFTyBTZXR0aW5ncyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuZm9ybVNlY3Rpb259PlxuICAgICAgICAgICAgICA8aDM+U0VPIFNldHRpbmdzPC9oMz5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuZm9ybUdyb3VwfT5cbiAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cInNlb1RpdGxlXCI+U0VPIFRpdGxlPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIGlkPVwic2VvVGl0bGVcIlxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnNlbz8udGl0bGUgfHwgJyd9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZU5lc3RlZElucHV0Q2hhbmdlKCdzZW8nLCAndGl0bGUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNFTyB0aXRsZSBmb3Igc2VhcmNoIGVuZ2luZXNcIlxuICAgICAgICAgICAgICAgICAgbWF4TGVuZ3RoPXs3MH1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDxzbWFsbD57KGZvcm1EYXRhLnNlbz8udGl0bGUgfHwgJycpLmxlbmd0aH0vNzAgY2hhcmFjdGVyczwvc21hbGw+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuZm9ybUdyb3VwfT5cbiAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cInNlb0Rlc2NyaXB0aW9uXCI+U0VPIERlc2NyaXB0aW9uPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgIGlkPVwic2VvRGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnNlbz8uZGVzY3JpcHRpb24gfHwgJyd9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZU5lc3RlZElucHV0Q2hhbmdlKCdzZW8nLCAnZGVzY3JpcHRpb24nLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNFTyBkZXNjcmlwdGlvbiBmb3Igc2VhcmNoIGVuZ2luZXNcIlxuICAgICAgICAgICAgICAgICAgbWF4TGVuZ3RoPXsxNjB9XG4gICAgICAgICAgICAgICAgICByb3dzPXszfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPHNtYWxsPnsoZm9ybURhdGEuc2VvPy5kZXNjcmlwdGlvbiB8fCAnJykubGVuZ3RofS8xNjAgY2hhcmFjdGVyczwvc21hbGw+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuZm9ybUdyb3VwfT5cbiAgICAgICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cInNlb1NsdWdcIj5VUkwgU2x1ZzwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICBpZD1cInNlb1NsdWdcIlxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnNlbz8uc2x1ZyB8fCAnJ31cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlTmVzdGVkSW5wdXRDaGFuZ2UoJ3NlbycsICdzbHVnJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJ1cmwtc2x1Zy1mb3ItY29sbGVjdGlvblwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHtlcnJvcnMuc3VibWl0ICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuc3VibWl0RXJyb3J9PlxuICAgICAgICAgICAgICA8QWxlcnRDaXJjbGUgc2l6ZT17MTZ9IC8+XG4gICAgICAgICAgICAgIHtlcnJvcnMuc3VibWl0fVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubW9kYWxBY3Rpb25zfT5cbiAgICAgICAgICAgIDxidXR0b24gdHlwZT1cImJ1dHRvblwiIG9uQ2xpY2s9e29uQ2xvc2V9IGNsYXNzTmFtZT17c3R5bGVzLmNhbmNlbEJ1dHRvbn0+XG4gICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8YnV0dG9uIHR5cGU9XCJzdWJtaXRcIiBkaXNhYmxlZD17aXNMb2FkaW5nfSBjbGFzc05hbWU9e3N0eWxlcy5zYXZlQnV0dG9ufT5cbiAgICAgICAgICAgICAge2lzTG9hZGluZyA/ICdTYXZpbmcuLi4nIDogKG1vZGUgPT09ICdjcmVhdGUnID8gJ0NyZWF0ZSBDb2xsZWN0aW9uJyA6ICdTYXZlIENoYW5nZXMnKX1cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Zvcm0+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIlgiLCJBbGVydENpcmNsZSIsImNvbGxlY3Rpb25zQXBpIiwic3R5bGVzIiwiQ29sbGVjdGlvbk1vZGFsIiwiaXNPcGVuIiwib25DbG9zZSIsIm9uU2F2ZSIsImNvbGxlY3Rpb24iLCJwYXJlbnRDb2xsZWN0aW9uIiwibW9kZSIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiY29sbGVjdGlvblR5cGUiLCJwdWJsaXNoZWQiLCJzZW8iLCJzbHVnIiwiaW1hZ2UiLCJ1cmwiLCJhbHRUZXh0IiwiZXJyb3JzIiwic2V0RXJyb3JzIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiYXZhaWxhYmxlUGFyZW50cyIsInNldEF2YWlsYWJsZVBhcmVudHMiLCJwYXJlbnQiLCJfaWQiLCJmZXRjaEF2YWlsYWJsZVBhcmVudHMiLCJyZXNwb25zZSIsImdldENvbGxlY3Rpb25zIiwibGltaXQiLCJ2YWxpZFBhcmVudHMiLCJjb2xsZWN0aW9ucyIsImZpbHRlciIsImMiLCJsZXZlbCIsInVuZGVmaW5lZCIsImVycm9yIiwiY29uc29sZSIsImdlbmVyYXRlSGFuZGxlIiwidG9Mb3dlckNhc2UiLCJyZXBsYWNlIiwiaGFuZGxlSW5wdXRDaGFuZ2UiLCJmaWVsZCIsInZhbHVlIiwicHJldiIsInVwZGF0ZWQiLCJoYW5kbGUiLCJoYW5kbGVOZXN0ZWRJbnB1dENoYW5nZSIsInZhbGlkYXRlRm9ybSIsIm5ld0Vycm9ycyIsInRyaW0iLCJmaW5kIiwicCIsIk9iamVjdCIsImtleXMiLCJsZW5ndGgiLCJoYW5kbGVTdWJtaXQiLCJlIiwicHJldmVudERlZmF1bHQiLCJzdWJtaXQiLCJtb2RhbFRpdGxlIiwiZGl2IiwiY2xhc3NOYW1lIiwibW9kYWxPdmVybGF5IiwibW9kYWxDb250YWluZXIiLCJtb2RhbEhlYWRlciIsImgyIiwiYnV0dG9uIiwib25DbGljayIsImNsb3NlQnV0dG9uIiwic2l6ZSIsImZvcm0iLCJvblN1Ym1pdCIsIm1vZGFsRm9ybSIsImZvcm1HcmlkIiwiZm9ybVNlY3Rpb24iLCJoMyIsImZvcm1Hcm91cCIsImxhYmVsIiwiaHRtbEZvciIsImlucHV0IiwiaWQiLCJ0eXBlIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJpbnB1dEVycm9yIiwicGxhY2Vob2xkZXIiLCJzcGFuIiwiZXJyb3JUZXh0Iiwic21hbGwiLCJ0ZXh0YXJlYSIsInJvd3MiLCJzZWxlY3QiLCJvcHRpb24iLCJtYXAiLCJyZXBlYXQiLCJjaGVja2JveExhYmVsIiwiY2hlY2tlZCIsIm1heExlbmd0aCIsInN1Ym1pdEVycm9yIiwibW9kYWxBY3Rpb25zIiwiY2FuY2VsQnV0dG9uIiwiZGlzYWJsZWQiLCJzYXZlQnV0dG9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/CollectionModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AdminContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/AdminContext.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminProvider: () => (/* binding */ AdminProvider),\n/* harmony export */   useAdmin: () => (/* binding */ useAdmin)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AdminProvider,useAdmin auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst initialState = {\n    admin: null,\n    isLoading: false,\n    sidebarCollapsed: false,\n    notifications: []\n};\nconst adminReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_ADMIN':\n            return {\n                ...state,\n                admin: action.payload,\n                isLoading: false\n            };\n        case 'CLEAR_ADMIN':\n            return {\n                ...state,\n                admin: null,\n                isLoading: false\n            };\n        case 'SET_LOADING':\n            return {\n                ...state,\n                isLoading: action.payload\n            };\n        case 'TOGGLE_SIDEBAR':\n            return {\n                ...state,\n                sidebarCollapsed: !state.sidebarCollapsed\n            };\n        case 'SET_SIDEBAR_COLLAPSED':\n            return {\n                ...state,\n                sidebarCollapsed: action.payload\n            };\n        case 'ADD_NOTIFICATION':\n            return {\n                ...state,\n                notifications: [\n                    ...state.notifications,\n                    {\n                        ...action.payload,\n                        id: Math.random().toString(36).substr(2, 9),\n                        timestamp: new Date()\n                    }\n                ]\n            };\n        case 'REMOVE_NOTIFICATION':\n            return {\n                ...state,\n                notifications: state.notifications.filter((n)=>n.id !== action.payload)\n            };\n        case 'CLEAR_NOTIFICATIONS':\n            return {\n                ...state,\n                notifications: []\n            };\n        default:\n            return state;\n    }\n};\nconst AdminContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst AdminProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(adminReducer, initialState);\n    // Load admin data on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminProvider.useEffect\": ()=>{\n            const loadAdminData = {\n                \"AdminProvider.useEffect.loadAdminData\": async ()=>{\n                    const token = localStorage.getItem('adminToken');\n                    if (!token) return;\n                    dispatch({\n                        type: 'SET_LOADING',\n                        payload: true\n                    });\n                    try {\n                        const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n                        const response = await fetch(\"\".concat(API_BASE_URL, \"/admin/profile\"), {\n                            headers: {\n                                'Authorization': \"Bearer \".concat(token)\n                            }\n                        });\n                        if (response.ok) {\n                            const data = await response.json();\n                            dispatch({\n                                type: 'SET_ADMIN',\n                                payload: data.admin\n                            });\n                        } else {\n                            localStorage.removeItem('adminToken');\n                            dispatch({\n                                type: 'CLEAR_ADMIN'\n                            });\n                        }\n                    } catch (error) {\n                        console.error('Failed to load admin data:', error);\n                        localStorage.removeItem('adminToken');\n                        dispatch({\n                            type: 'CLEAR_ADMIN'\n                        });\n                    }\n                }\n            }[\"AdminProvider.useEffect.loadAdminData\"];\n            loadAdminData();\n        }\n    }[\"AdminProvider.useEffect\"], []);\n    // Load sidebar state from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminProvider.useEffect\": ()=>{\n            const savedSidebarState = localStorage.getItem('adminSidebarCollapsed');\n            if (savedSidebarState) {\n                dispatch({\n                    type: 'SET_SIDEBAR_COLLAPSED',\n                    payload: JSON.parse(savedSidebarState)\n                });\n            }\n        }\n    }[\"AdminProvider.useEffect\"], []);\n    // Save sidebar state to localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminProvider.useEffect\": ()=>{\n            localStorage.setItem('adminSidebarCollapsed', JSON.stringify(state.sidebarCollapsed));\n        }\n    }[\"AdminProvider.useEffect\"], [\n        state.sidebarCollapsed\n    ]);\n    const hasPermission = (resource, action)=>{\n        if (!state.admin) return false;\n        if (state.admin.role === 'super_admin') return true;\n        const resourcePermissions = state.admin.permissions[resource];\n        if (!resourcePermissions) return false;\n        return resourcePermissions[action] || false;\n    };\n    const addNotification = (notification)=>{\n        dispatch({\n            type: 'ADD_NOTIFICATION',\n            payload: notification\n        });\n        const id = Math.random().toString(36).substr(2, 9);\n        setTimeout(()=>{\n            dispatch({\n                type: 'REMOVE_NOTIFICATION',\n                payload: id\n            });\n        }, 5000);\n    };\n    const removeNotification = (id)=>{\n        dispatch({\n            type: 'REMOVE_NOTIFICATION',\n            payload: id\n        });\n    };\n    const logout = async ()=>{\n        try {\n            const token = localStorage.getItem('adminToken');\n            if (token) {\n                const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n                await fetch(\"\".concat(API_BASE_URL, \"/admin/logout\"), {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': \"Bearer \".concat(token)\n                    }\n                });\n            }\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            localStorage.removeItem('adminToken');\n            dispatch({\n                type: 'CLEAR_ADMIN'\n            });\n            window.location.href = '/admin/login';\n        }\n    };\n    const apiCall = async function(url) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const token = localStorage.getItem('adminToken');\n        const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n        const headers = {\n            'Content-Type': 'application/json',\n            ...token ? {\n                'Authorization': \"Bearer \".concat(token)\n            } : {},\n            ...options.headers || {}\n        };\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL).concat(url), {\n                ...options,\n                headers\n            });\n            const data = await response.json();\n            return data;\n        } catch (error) {\n            return {\n                success: false,\n                message: 'Network error',\n                error\n            };\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdminContext.Provider, {\n        value: {\n            state,\n            dispatch,\n            hasPermission,\n            addNotification,\n            removeNotification,\n            logout,\n            apiCall\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\contexts\\\\AdminContext.tsx\",\n        lineNumber: 242,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AdminProvider, \"s3jE+e7wLGXN/2uWqdAG2uRSMfA=\");\n_c = AdminProvider;\nconst useAdmin = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AdminContext);\n    if (!context) {\n        throw new Error('useAdmin must be used within an AdminProvider');\n    }\n    return context;\n};\n_s1(useAdmin, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AdminProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AdminContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError),\n/* harmony export */   addToOfflineQueue: () => (/* binding */ addToOfflineQueue),\n/* harmony export */   cartApi: () => (/* binding */ cartApi),\n/* harmony export */   collectionsApi: () => (/* binding */ collectionsApi),\n/* harmony export */   offlineQueue: () => (/* binding */ offlineQueue),\n/* harmony export */   ordersApi: () => (/* binding */ ordersApi),\n/* harmony export */   processOfflineQueue: () => (/* binding */ processOfflineQueue),\n/* harmony export */   productsApi: () => (/* binding */ productsApi),\n/* harmony export */   retryRequest: () => (/* binding */ retryRequest)\n/* harmony export */ });\n/* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-explicit-any */ const API_BASE_URL = 'http://localhost:5000/api';\n// Error handling utility\nclass ApiError extends Error {\n    constructor(message, status, code){\n        super(message), this.status = status, this.code = code;\n        this.name = 'ApiError';\n    }\n}\n// Request utility with error handling\nasync function apiRequest(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const url = \"\".concat(API_BASE_URL).concat(endpoint);\n    // Get auth token from localStorage\n    const token =  true ? localStorage.getItem('authToken') : 0;\n    const defaultHeaders = {\n        'Content-Type': 'application/json'\n    };\n    if (token) {\n        defaultHeaders.Authorization = \"Bearer \".concat(token);\n    }\n    const config = {\n        ...options,\n        headers: {\n            ...defaultHeaders,\n            ...options.headers\n        }\n    };\n    try {\n        const response = await fetch(url, config);\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new ApiError(errorData.message || \"HTTP \".concat(response.status, \": \").concat(response.statusText), response.status, errorData.code);\n        }\n        const data = await response.json();\n        if (!data.success) {\n            throw new ApiError(data.message || 'API request failed', undefined, data.code);\n        }\n        return data;\n    } catch (error) {\n        if (error instanceof ApiError) {\n            throw error;\n        }\n        // Network or other errors\n        if (error instanceof TypeError && error.message.includes('fetch')) {\n            throw new ApiError('Network error. Please check your connection.', 0, 'NETWORK_ERROR');\n        }\n        throw new ApiError('An unexpected error occurred.', 0, 'UNKNOWN_ERROR');\n    }\n}\n// Cart API\nconst cartApi = {\n    // Get user's cart\n    async getCart () {\n        return apiRequest('/cart');\n    },\n    // Add item to cart\n    async addToCart (productId) {\n        let quantity = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        return apiRequest('/cart/add', {\n            method: 'POST',\n            body: JSON.stringify({\n                productId,\n                quantity\n            })\n        });\n    },\n    // Update item quantity\n    async updateCartItem (productId, quantity) {\n        return apiRequest(\"/cart/item/\".concat(productId), {\n            method: 'PUT',\n            body: JSON.stringify({\n                quantity\n            })\n        });\n    },\n    // Remove item from cart\n    async removeFromCart (productId) {\n        return apiRequest(\"/cart/item/\".concat(productId), {\n            method: 'DELETE'\n        });\n    },\n    // Clear entire cart\n    async clearCart () {\n        return apiRequest('/cart/clear', {\n            method: 'DELETE'\n        });\n    },\n    // Sync cart with frontend\n    async syncCart (items) {\n        return apiRequest('/cart/sync', {\n            method: 'POST',\n            body: JSON.stringify({\n                items\n            })\n        });\n    }\n};\n// Orders API\nconst ordersApi = {\n    // Create payment intent\n    async createPaymentIntent (amount) {\n        const response = await apiRequest('/orders/payment-intent', {\n            method: 'POST',\n            body: JSON.stringify({\n                amount\n            })\n        });\n        return response;\n    },\n    // Create order after successful payment\n    async createOrder (paymentIntentId, shippingAddress, paymentMethod) {\n        return apiRequest('/orders/create', {\n            method: 'POST',\n            body: JSON.stringify({\n                paymentIntentId,\n                shippingAddress,\n                paymentMethod\n            })\n        });\n    },\n    // Get user's orders\n    async getUserOrders () {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n        return apiRequest(\"/orders?page=\".concat(page, \"&limit=\").concat(limit));\n    },\n    // Get specific order\n    async getOrder (orderNumber) {\n        return apiRequest(\"/orders/\".concat(orderNumber));\n    },\n    // Handle payment failure\n    async handlePaymentFailure (paymentIntentId, error) {\n        return apiRequest('/orders/payment-failure', {\n            method: 'POST',\n            body: JSON.stringify({\n                paymentIntentId,\n                error\n            })\n        });\n    }\n};\n// Products API (if needed for cart integration)\nconst productsApi = {\n    // Get all products\n    async getProducts (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.category) searchParams.append('category', params.category);\n        if (params === null || params === void 0 ? void 0 : params.tags) searchParams.append('tags', params.tags);\n        if (params === null || params === void 0 ? void 0 : params.collectionId) searchParams.append('collectionId', params.collectionId);\n        if (params === null || params === void 0 ? void 0 : params.collectionPath) searchParams.append('collectionPath', params.collectionPath);\n        if (params === null || params === void 0 ? void 0 : params.includeDescendants) searchParams.append('includeDescendants', params.includeDescendants.toString());\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.sortBy) searchParams.append('sortBy', params.sortBy);\n        if (params === null || params === void 0 ? void 0 : params.sortOrder) searchParams.append('sortOrder', params.sortOrder);\n        const queryString = searchParams.toString();\n        const endpoint = queryString ? \"/products?\".concat(queryString) : '/products';\n        return apiRequest(endpoint);\n    },\n    // Get products by collection path\n    async getProductsByCollectionPath (path, params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.sortBy) searchParams.append('sortBy', params.sortBy);\n        if (params === null || params === void 0 ? void 0 : params.sortOrder) searchParams.append('sortOrder', params.sortOrder);\n        if (params === null || params === void 0 ? void 0 : params.includeDescendants) searchParams.append('includeDescendants', params.includeDescendants.toString());\n        const queryString = searchParams.toString();\n        const endpoint = queryString ? \"/products/collection/\".concat(path, \"?\").concat(queryString) : \"/products/collection/\".concat(path);\n        return apiRequest(endpoint);\n    },\n    // Get single product\n    async getProduct (id) {\n        return apiRequest(\"/products/\".concat(id));\n    }\n};\n// Collections API for hierarchical collections\nconst collectionsApi = {\n    // Get all collections with hierarchy support\n    async getCollections (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if ((params === null || params === void 0 ? void 0 : params.level) !== undefined) searchParams.append('level', params.level.toString());\n        if (params === null || params === void 0 ? void 0 : params.parentId) searchParams.append('parentId', params.parentId);\n        if (params === null || params === void 0 ? void 0 : params.hierarchy) searchParams.append('hierarchy', params.hierarchy.toString());\n        if ((params === null || params === void 0 ? void 0 : params.published) !== undefined) searchParams.append('published', params.published.toString());\n        const queryString = searchParams.toString();\n        const endpoint = queryString ? \"/collections?\".concat(queryString) : '/collections';\n        return apiRequest(endpoint);\n    },\n    // Get collection hierarchy\n    async getCollectionHierarchy () {\n        return apiRequest('/collections?hierarchy=true');\n    },\n    // Get collections by level\n    async getCollectionsByLevel (level) {\n        return apiRequest(\"/collections?level=\".concat(level));\n    },\n    // Get root collections\n    async getRootCollections () {\n        return apiRequest('/collections?level=0&published=true');\n    },\n    // Get single collection\n    async getCollection (id) {\n        return apiRequest(\"/collections/\".concat(id));\n    },\n    // Get collection by path\n    async getCollectionByPath (path) {\n        return apiRequest(\"/collections/path/\".concat(path));\n    },\n    // Get collection breadcrumbs\n    async getCollectionBreadcrumbs (id) {\n        return apiRequest(\"/collections/\".concat(id, \"/breadcrumbs\"));\n    }\n};\n// Retry utility for failed requests\nconst retryRequest = async function(requestFn) {\n    let maxRetries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3, delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1000;\n    let lastError;\n    for(let i = 0; i <= maxRetries; i++){\n        try {\n            return await requestFn();\n        } catch (error) {\n            lastError = error;\n            // Don't retry on client errors (4xx) except 408, 429\n            if (error instanceof ApiError && error.status) {\n                if (error.status >= 400 && error.status < 500 && error.status !== 408 && error.status !== 429) {\n                    throw error;\n                }\n            }\n            if (i < maxRetries) {\n                await new Promise((resolve)=>setTimeout(resolve, delay * Math.pow(2, i)));\n            }\n        }\n    }\n    throw lastError;\n};\n// Offline detection and queue\nconst offlineQueue = [];\nconst addToOfflineQueue = (requestFn)=>{\n    offlineQueue.push(requestFn);\n};\nconst processOfflineQueue = async ()=>{\n    if (!navigator.onLine || offlineQueue.length === 0) return;\n    const requests = [\n        ...offlineQueue\n    ];\n    offlineQueue.length = 0; // Clear the queue\n    for (const request of requests){\n        try {\n            await request();\n        } catch (error) {\n            console.error('Failed to process offline request:', error);\n            // Re-add to queue if it fails\n            offlineQueue.push(request);\n        }\n    }\n};\n// Listen for online events to process queue\nif (true) {\n    window.addEventListener('online', processOfflineQueue);\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/api.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Ccollections%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);