"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!******************************************!*\
  !*** ./src/components/home/<USER>
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../index */ \"(app-pages-browser)/./src/components/index.ts\");\n/* harmony import */ var _HomePage_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HomePage.module.css */ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _HomePage_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_HomePage_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction HomePage() {\n    const featuredProducts = [\n        {\n            id: 1,\n            title: \"DESIGNER'S PICKS\",\n            subtitle: \"A peek inside our designer's shopping cart.\",\n            image: \"/images/fireplace-collection.jpg\",\n            buttonText: \"SHOP NOW\",\n            link: \"/collections/designer-picks\"\n        },\n        {\n            id: 2,\n            title: \"THE CAST STONE SHOP\",\n            subtitle: \"The best of the best, from fireplaces and fountains to architectural elements.\",\n            image: \"/images/garden-collection.jpg\",\n            buttonText: \"SHOP NOW\",\n            link: \"/collections/cast-stone\"\n        },\n        {\n            id: 3,\n            title: \"ARCHITECTURAL ELEMENTS\",\n            subtitle: \"Clean, luxurious, results-driven architectural cast stone pieces.\",\n            image: \"/images/architectural-collection.jpg\",\n            buttonText: \"SHOP NOW\",\n            link: \"/collections/architectural\"\n        },\n        {\n            id: 4,\n            title: \"PREMIUM COLLECTION\",\n            subtitle: \"Classics, reimagined for the modern home.\",\n            image: \"/images/hero-cast-stone.jpg\",\n            buttonText: \"SHOP NOW\",\n            link: \"/collections/premium\"\n        }\n    ];\n    const testimonials = [\n        {\n            id: 1,\n            name: \"Sarah Johnson\",\n            company: \"Johnson Architecture\",\n            text: \"Cast Stone's architectural elements transformed our project. The quality and craftsmanship are unmatched.\",\n            rating: 5\n        },\n        {\n            id: 2,\n            name: \"Michael Chen\",\n            company: \"Elite Homes\",\n            text: \"We've been using Cast Stone products for over 10 years. Their consistency and beauty never disappoint.\",\n            rating: 5\n        },\n        {\n            id: 3,\n            name: \"Emma Rodriguez\",\n            company: \"Rodriguez Design Studio\",\n            text: \"The limited edition pieces add such elegance to our high-end residential projects.\",\n            rating: 5\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_HomePage_module_css__WEBPACK_IMPORTED_MODULE_2___default().container),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.Navigation, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.HeroSection, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.CollectionsGrid, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.CatalogSection, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.FeaturedCollections, {\n                featuredProducts: featuredProducts\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.TestimonialsSection, {\n                testimonials: testimonials\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.Footer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});