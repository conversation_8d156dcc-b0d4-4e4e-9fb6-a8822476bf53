"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!******************************************!*\
  !*** ./src/components/home/<USER>
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../index */ \"(app-pages-browser)/./src/components/index.ts\");\n/* harmony import */ var _HomePage_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./HomePage.module.css */ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _HomePage_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_HomePage_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction HomePage() {\n    const featuredProducts = [\n        {\n            id: 1,\n            title: \"DESIGNER'S PICKS\",\n            subtitle: \"A peek inside our designer's shopping cart.\",\n            image: \"/images/fireplace-collection.jpg\",\n            buttonText: \"SHOP NOW\",\n            link: \"/collections/designer-picks\"\n        },\n        {\n            id: 2,\n            title: \"THE CAST STONE SHOP\",\n            subtitle: \"The best of the best, from fireplaces and fountains to architectural elements.\",\n            image: \"/images/garden-collection.jpg\",\n            buttonText: \"SHOP NOW\",\n            link: \"/collections/cast-stone\"\n        },\n        {\n            id: 3,\n            title: \"ARCHITECTURAL ELEMENTS\",\n            subtitle: \"Clean, luxurious, results-driven architectural cast stone pieces.\",\n            image: \"/images/architectural-collection.jpg\",\n            buttonText: \"SHOP NOW\",\n            link: \"/collections/architectural\"\n        },\n        {\n            id: 4,\n            title: \"PREMIUM COLLECTION\",\n            subtitle: \"Classics, reimagined for the modern home.\",\n            image: \"/images/hero-cast-stone.jpg\",\n            buttonText: \"SHOP NOW\",\n            link: \"/collections/premium\"\n        }\n    ];\n    const testimonials = [\n        {\n            id: 1,\n            name: \"Sarah Johnson\",\n            company: \"Johnson Architecture\",\n            text: \"Cast Stone's architectural elements transformed our project. The quality and craftsmanship are unmatched.\",\n            rating: 5\n        },\n        {\n            id: 2,\n            name: \"Michael Chen\",\n            company: \"Elite Homes\",\n            text: \"We've been using Cast Stone products for over 10 years. Their consistency and beauty never disappoint.\",\n            rating: 5\n        },\n        {\n            id: 3,\n            name: \"Emma Rodriguez\",\n            company: \"Rodriguez Design Studio\",\n            text: \"The limited edition pieces add such elegance to our high-end residential projects.\",\n            rating: 5\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_HomePage_module_css__WEBPACK_IMPORTED_MODULE_2___default().container),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.Navigation, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.HeroSection, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.CollectionsGrid, {\n                categories: categories\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.CatalogSection, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.FeaturedCollections, {\n                featuredProducts: featuredProducts\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.TestimonialsSection, {\n                testimonials: testimonials\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_index__WEBPACK_IMPORTED_MODULE_1__.Footer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\HomePage.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});