"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!*************************************************!*\
  !*** ./src/components/home/<USER>
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CollectionsGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./CollectionsGrid.module.css */ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CollectionsGrid(param) {\n    let { className } = param;\n    _s();\n    const [collections, setCollections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fetchCollections = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"CollectionsGrid.useCallback[fetchCollections]\": async ()=>{\n            try {\n                setIsLoading(true);\n                setError(null);\n                // Get only root collections for the homepage\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_4__.collectionsApi.getRootCollections();\n                setCollections(response.collections || []);\n            } catch (error) {\n                console.error('Failed to fetch collections:', error);\n                setError('Failed to load collections');\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"CollectionsGrid.useCallback[fetchCollections]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CollectionsGrid.useEffect\": ()=>{\n            fetchCollections();\n        }\n    }[\"CollectionsGrid.useEffect\"], [\n        fetchCollections\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"\".concat((_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().collectionsSection), \" \").concat(className || ''),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().loadingContainer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().spinner)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Loading collections...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || collections.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"\".concat((_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().collectionsSection), \" \").concat(className || ''),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().errorContainer),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Unable to load collections at this time.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"\".concat((_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().collectionsSection), \" \").concat(className || ''),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().collectionsGrid),\n            children: collections.map((collection)=>{\n                var _collection_image, _collection_image1, _collection_children;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().collectionCard),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().collectionImage),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: ((_collection_image = collection.image) === null || _collection_image === void 0 ? void 0 : _collection_image.url) || '/images/placeholder-collection.jpg',\n                                alt: ((_collection_image1 = collection.image) === null || _collection_image1 === void 0 ? void 0 : _collection_image1.altText) || collection.title,\n                                fill: true,\n                                className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().collectionImg)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().collectionOverlay),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().collectionBrand),\n                                    children: collection.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().collectionSubtitle),\n                                    children: ((_collection_children = collection.children) === null || _collection_children === void 0 ? void 0 : _collection_children.length) > 0 ? \"\".concat(collection.children.length, \" Categories\") : 'Premium Collection'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().collectionDescription),\n                                    children: collection.description || 'Explore our premium cast stone collection'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().collectionPrice),\n                                    children: \"Starting from $299\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().collectionButtons),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/collections/\".concat(collection.handle),\n                                            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().buildBtn),\n                                            children: \"EXPLORE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/collections/\".concat(collection.handle),\n                                            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_5___default().allModelsBtn),\n                                            children: \"VIEW ALL\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, collection._id, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 11\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(CollectionsGrid, \"N7noRT/uQX8m9n8qt9HrCZXU508=\");\n_c = CollectionsGrid;\nvar _c;\n$RefreshReg$(_c, \"CollectionsGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError),\n/* harmony export */   addToOfflineQueue: () => (/* binding */ addToOfflineQueue),\n/* harmony export */   cartApi: () => (/* binding */ cartApi),\n/* harmony export */   collectionsApi: () => (/* binding */ collectionsApi),\n/* harmony export */   offlineQueue: () => (/* binding */ offlineQueue),\n/* harmony export */   ordersApi: () => (/* binding */ ordersApi),\n/* harmony export */   processOfflineQueue: () => (/* binding */ processOfflineQueue),\n/* harmony export */   productsApi: () => (/* binding */ productsApi),\n/* harmony export */   retryRequest: () => (/* binding */ retryRequest)\n/* harmony export */ });\n/* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-explicit-any */ const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n// Error handling utility\nclass ApiError extends Error {\n    constructor(message, status, code){\n        super(message), this.status = status, this.code = code;\n        this.name = 'ApiError';\n    }\n}\n// Request utility with error handling\nasync function apiRequest(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const url = \"\".concat(API_BASE_URL).concat(endpoint);\n    // Get auth token from localStorage\n    const token =  true ? localStorage.getItem('authToken') : 0;\n    const defaultHeaders = {\n        'Content-Type': 'application/json'\n    };\n    if (token) {\n        defaultHeaders.Authorization = \"Bearer \".concat(token);\n    }\n    const config = {\n        ...options,\n        headers: {\n            ...defaultHeaders,\n            ...options.headers\n        }\n    };\n    try {\n        const response = await fetch(url, config);\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new ApiError(errorData.message || \"HTTP \".concat(response.status, \": \").concat(response.statusText), response.status, errorData.code);\n        }\n        const data = await response.json();\n        if (!data.success) {\n            throw new ApiError(data.message || 'API request failed', undefined, data.code);\n        }\n        return data;\n    } catch (error) {\n        if (error instanceof ApiError) {\n            throw error;\n        }\n        // Network or other errors\n        if (error instanceof TypeError && error.message.includes('fetch')) {\n            throw new ApiError('Network error. Please check your connection.', 0, 'NETWORK_ERROR');\n        }\n        throw new ApiError('An unexpected error occurred.', 0, 'UNKNOWN_ERROR');\n    }\n}\n// Cart API\nconst cartApi = {\n    // Get user's cart\n    async getCart () {\n        return apiRequest('/cart');\n    },\n    // Add item to cart\n    async addToCart (productId) {\n        let quantity = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        return apiRequest('/cart/add', {\n            method: 'POST',\n            body: JSON.stringify({\n                productId,\n                quantity\n            })\n        });\n    },\n    // Update item quantity\n    async updateCartItem (productId, quantity) {\n        return apiRequest(\"/cart/item/\".concat(productId), {\n            method: 'PUT',\n            body: JSON.stringify({\n                quantity\n            })\n        });\n    },\n    // Remove item from cart\n    async removeFromCart (productId) {\n        return apiRequest(\"/cart/item/\".concat(productId), {\n            method: 'DELETE'\n        });\n    },\n    // Clear entire cart\n    async clearCart () {\n        return apiRequest('/cart/clear', {\n            method: 'DELETE'\n        });\n    },\n    // Sync cart with frontend\n    async syncCart (items) {\n        return apiRequest('/cart/sync', {\n            method: 'POST',\n            body: JSON.stringify({\n                items\n            })\n        });\n    }\n};\n// Orders API\nconst ordersApi = {\n    // Create payment intent\n    async createPaymentIntent (amount) {\n        const response = await apiRequest('/orders/payment-intent', {\n            method: 'POST',\n            body: JSON.stringify({\n                amount\n            })\n        });\n        return response;\n    },\n    // Create order after successful payment\n    async createOrder (paymentIntentId, shippingAddress, paymentMethod) {\n        return apiRequest('/orders/create', {\n            method: 'POST',\n            body: JSON.stringify({\n                paymentIntentId,\n                shippingAddress,\n                paymentMethod\n            })\n        });\n    },\n    // Get user's orders\n    async getUserOrders () {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n        return apiRequest(\"/orders?page=\".concat(page, \"&limit=\").concat(limit));\n    },\n    // Get specific order\n    async getOrder (orderNumber) {\n        return apiRequest(\"/orders/\".concat(orderNumber));\n    },\n    // Handle payment failure\n    async handlePaymentFailure (paymentIntentId, error) {\n        return apiRequest('/orders/payment-failure', {\n            method: 'POST',\n            body: JSON.stringify({\n                paymentIntentId,\n                error\n            })\n        });\n    }\n};\n// Products API (if needed for cart integration)\nconst productsApi = {\n    // Get all products\n    async getProducts (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.category) searchParams.append('category', params.category);\n        if (params === null || params === void 0 ? void 0 : params.tags) searchParams.append('tags', params.tags);\n        if (params === null || params === void 0 ? void 0 : params.collectionId) searchParams.append('collectionId', params.collectionId);\n        if (params === null || params === void 0 ? void 0 : params.collectionPath) searchParams.append('collectionPath', params.collectionPath);\n        if (params === null || params === void 0 ? void 0 : params.includeDescendants) searchParams.append('includeDescendants', params.includeDescendants.toString());\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.sortBy) searchParams.append('sortBy', params.sortBy);\n        if (params === null || params === void 0 ? void 0 : params.sortOrder) searchParams.append('sortOrder', params.sortOrder);\n        const queryString = searchParams.toString();\n        const endpoint = queryString ? \"/products?\".concat(queryString) : '/products';\n        return apiRequest(endpoint);\n    },\n    // Get products by collection path\n    async getProductsByCollectionPath (path, params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.sortBy) searchParams.append('sortBy', params.sortBy);\n        if (params === null || params === void 0 ? void 0 : params.sortOrder) searchParams.append('sortOrder', params.sortOrder);\n        if (params === null || params === void 0 ? void 0 : params.includeDescendants) searchParams.append('includeDescendants', params.includeDescendants.toString());\n        const queryString = searchParams.toString();\n        const endpoint = queryString ? \"/products/collection/\".concat(path, \"?\").concat(queryString) : \"/products/collection/\".concat(path);\n        return apiRequest(endpoint);\n    },\n    // Get single product\n    async getProduct (id) {\n        return apiRequest(\"/products/\".concat(id));\n    }\n};\n// Collections API for hierarchical collections\nconst collectionsApi = {\n    // Get all collections with hierarchy support\n    async getCollections (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if ((params === null || params === void 0 ? void 0 : params.level) !== undefined) searchParams.append('level', params.level.toString());\n        if (params === null || params === void 0 ? void 0 : params.parentId) searchParams.append('parentId', params.parentId);\n        if (params === null || params === void 0 ? void 0 : params.hierarchy) searchParams.append('hierarchy', params.hierarchy.toString());\n        if ((params === null || params === void 0 ? void 0 : params.published) !== undefined) searchParams.append('published', params.published.toString());\n        const queryString = searchParams.toString();\n        const endpoint = queryString ? \"/collections?\".concat(queryString) : '/collections';\n        return apiRequest(endpoint);\n    },\n    // Get collection hierarchy\n    async getCollectionHierarchy () {\n        return apiRequest('/collections?hierarchy=true');\n    },\n    // Get collections by level\n    async getCollectionsByLevel (level) {\n        return apiRequest(\"/collections?level=\".concat(level));\n    },\n    // Get root collections\n    async getRootCollections () {\n        return apiRequest('/collections?level=0&published=true');\n    },\n    // Get single collection\n    async getCollection (id) {\n        return apiRequest(\"/collections/\".concat(id));\n    },\n    // Get collection by path\n    async getCollectionByPath (path) {\n        return apiRequest(\"/collections/path/\".concat(path));\n    },\n    // Get collection breadcrumbs\n    async getCollectionBreadcrumbs (id) {\n        return apiRequest(\"/collections/\".concat(id, \"/breadcrumbs\"));\n    }\n};\n// Retry utility for failed requests\nconst retryRequest = async function(requestFn) {\n    let maxRetries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3, delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1000;\n    let lastError;\n    for(let i = 0; i <= maxRetries; i++){\n        try {\n            return await requestFn();\n        } catch (error) {\n            lastError = error;\n            // Don't retry on client errors (4xx) except 408, 429\n            if (error instanceof ApiError && error.status) {\n                if (error.status >= 400 && error.status < 500 && error.status !== 408 && error.status !== 429) {\n                    throw error;\n                }\n            }\n            if (i < maxRetries) {\n                await new Promise((resolve)=>setTimeout(resolve, delay * Math.pow(2, i)));\n            }\n        }\n    }\n    throw lastError;\n};\n// Offline detection and queue\nconst offlineQueue = [];\nconst addToOfflineQueue = (requestFn)=>{\n    offlineQueue.push(requestFn);\n};\nconst processOfflineQueue = async ()=>{\n    if (!navigator.onLine || offlineQueue.length === 0) return;\n    const requests = [\n        ...offlineQueue\n    ];\n    offlineQueue.length = 0; // Clear the queue\n    for (const request of requests){\n        try {\n            await request();\n        } catch (error) {\n            console.error('Failed to process offline request:', error);\n            // Re-add to queue if it fails\n            offlineQueue.push(request);\n        }\n    }\n};\n// Listen for online events to process queue\nif (true) {\n    window.addEventListener('online', processOfflineQueue);\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/api.ts\n"));

/***/ })

});