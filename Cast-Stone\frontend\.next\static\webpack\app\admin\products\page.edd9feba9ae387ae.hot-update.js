"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/page",{

/***/ "(app-pages-browser)/./src/app/admin/products/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/admin/products/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductsManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contexts/AdminContext */ \"(app-pages-browser)/./src/contexts/AdminContext.tsx\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./page.module.css */ \"(app-pages-browser)/./src/app/admin/products/page.module.css\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_page_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* eslint-disable @typescript-eslint/no-unused-vars */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ProductsManagement() {\n    _s();\n    const { hasPermission, addNotification } = (0,_contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__.useAdmin)();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddProductModal, setShowAddProductModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showImportModal, setShowImportModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Mock data - replace with actual API calls\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsManagement.useEffect\": ()=>{\n            const fetchProducts = {\n                \"ProductsManagement.useEffect.fetchProducts\": async ()=>{\n                    try {\n                        // Simulate API call\n                        await new Promise({\n                            \"ProductsManagement.useEffect.fetchProducts\": (resolve)=>setTimeout(resolve, 1000)\n                        }[\"ProductsManagement.useEffect.fetchProducts\"]);\n                        setProducts([\n                            {\n                                id: '1',\n                                name: 'Classic Fireplace Mantel',\n                                category: 'fireplaces',\n                                price: 2500,\n                                stock: 12,\n                                status: 'active',\n                                imageUrl: '/images/fireplace-collection.jpg',\n                                description: 'Handcrafted traditional mantel with intricate detailing',\n                                createdAt: '2024-01-10',\n                                updatedAt: '2024-01-15'\n                            },\n                            {\n                                id: '2',\n                                name: 'Modern Fireplace Surround',\n                                category: 'fireplaces',\n                                price: 3200,\n                                stock: 8,\n                                status: 'active',\n                                imageUrl: '/images/fireplace-collection.jpg',\n                                description: 'Contemporary design with clean lines',\n                                createdAt: '2024-01-12',\n                                updatedAt: '2024-01-14'\n                            },\n                            {\n                                id: '3',\n                                name: 'Garden Fountain',\n                                category: 'garden',\n                                price: 1800,\n                                stock: 5,\n                                status: 'active',\n                                imageUrl: '/images/garden-collection.jpg',\n                                description: 'Three-tier fountain perfect for outdoor spaces',\n                                createdAt: '2024-01-08',\n                                updatedAt: '2024-01-13'\n                            },\n                            {\n                                id: '4',\n                                name: 'Decorative Columns',\n                                category: 'architectural',\n                                price: 1200,\n                                stock: 0,\n                                status: 'inactive',\n                                imageUrl: '/images/architectural-collection.jpg',\n                                description: 'Corinthian style columns for grand entrances',\n                                createdAt: '2024-01-05',\n                                updatedAt: '2024-01-10'\n                            }\n                        ]);\n                    } catch (error) {\n                        console.error('Failed to fetch products:', error);\n                        addNotification({\n                            type: 'error',\n                            title: 'Error',\n                            message: 'Failed to load products'\n                        });\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"ProductsManagement.useEffect.fetchProducts\"];\n            fetchProducts();\n        }\n    }[\"ProductsManagement.useEffect\"], [\n        addNotification\n    ]);\n    const categories = [\n        'all',\n        'fireplaces',\n        'garden',\n        'architectural',\n        'decorative'\n    ];\n    const statuses = [\n        'all',\n        'active',\n        'inactive',\n        'draft'\n    ];\n    const filteredProducts = products.filter((product)=>{\n        const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.description.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n        const matchesStatus = selectedStatus === 'all' || product.status === selectedStatus;\n        return matchesSearch && matchesCategory && matchesStatus;\n    });\n    const handleSelectProduct = (productId)=>{\n        setSelectedProducts((prev)=>prev.includes(productId) ? prev.filter((id)=>id !== productId) : [\n                ...prev,\n                productId\n            ]);\n    };\n    const handleSelectAll = ()=>{\n        if (selectedProducts.length === filteredProducts.length) {\n            setSelectedProducts([]);\n        } else {\n            setSelectedProducts(filteredProducts.map((p)=>p.id));\n        }\n    };\n    const handleDeleteProduct = async (productId)=>{\n        if (!hasPermission('products', 'delete')) {\n            addNotification({\n                type: 'error',\n                title: 'Permission Denied',\n                message: 'You do not have permission to delete products'\n            });\n            return;\n        }\n        if (confirm('Are you sure you want to delete this product?')) {\n            try {\n                // Simulate API call\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                setProducts((prev)=>prev.filter((p)=>p.id !== productId));\n                addNotification({\n                    type: 'success',\n                    title: 'Product Deleted',\n                    message: 'Product has been successfully deleted'\n                });\n            } catch (error) {\n                addNotification({\n                    type: 'error',\n                    title: 'Error',\n                    message: 'Failed to delete product'\n                });\n            }\n        }\n    };\n    const handleBulkDelete = async ()=>{\n        if (!hasPermission('products', 'delete')) {\n            addNotification({\n                type: 'error',\n                title: 'Permission Denied',\n                message: 'You do not have permission to delete products'\n            });\n            return;\n        }\n        if (selectedProducts.length === 0) return;\n        if (confirm(\"Are you sure you want to delete \".concat(selectedProducts.length, \" products?\"))) {\n            try {\n                // Simulate API call\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                setProducts((prev)=>prev.filter((p)=>!selectedProducts.includes(p.id)));\n                setSelectedProducts([]);\n                addNotification({\n                    type: 'success',\n                    title: 'Products Deleted',\n                    message: \"\".concat(selectedProducts.length, \" products have been deleted\")\n                });\n            } catch (error) {\n                addNotification({\n                    type: 'error',\n                    title: 'Error',\n                    message: 'Failed to delete products'\n                });\n            }\n        }\n    };\n    const handleAddProduct = ()=>{\n        setShowAddProductModal(true);\n    };\n    const handleImport = ()=>{\n        setShowImportModal(true);\n    };\n    const handleExport = async ()=>{\n        try {\n            addNotification({\n                type: 'info',\n                title: 'Export Started',\n                message: 'Your product export is being prepared...'\n            });\n            // Simulate export process\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // Create CSV content\n            const csvContent = [\n                [\n                    'Name',\n                    'Category',\n                    'Price',\n                    'Stock',\n                    'Status'\n                ],\n                ...filteredProducts.map((product)=>[\n                        product.name,\n                        product.category,\n                        product.price.toString(),\n                        product.stock.toString(),\n                        product.status\n                    ])\n            ].map((row)=>row.join(',')).join('\\n');\n            // Create and download file\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: 'text/csv'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"products-export-\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            window.URL.revokeObjectURL(url);\n            addNotification({\n                type: 'success',\n                title: 'Export Complete',\n                message: 'Products have been exported successfully'\n            });\n        } catch (error) {\n            addNotification({\n                type: 'error',\n                title: 'Export Failed',\n                message: 'Failed to export products'\n            });\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().loading),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().spinner)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading products...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n            lineNumber: 267,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productsPage),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().title),\n                                children: \"Products Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().subtitle),\n                                children: \"Manage your product catalog, inventory, and pricing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().headerActions),\n                        children: [\n                            hasPermission('products', 'create') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().primaryButton),\n                                onClick: handleAddProduct,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Product\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryButton),\n                                onClick: handleImport,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Import\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryButton),\n                                onClick: handleExport,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().toolbar),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().searchContainer),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().searchIcon)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search products...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().searchInput)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filters),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterButton), \" \").concat(showFilters ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                                onClick: ()=>setShowFilters(!showFilters),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Filters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, this),\n                            showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterDropdown),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterGroup),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedCategory,\n                                                onChange: (e)=>setSelectedCategory(e.target.value),\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: category,\n                                                        children: category === 'all' ? 'All Categories' : category\n                                                    }, category, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterGroup),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedStatus,\n                                                onChange: (e)=>setSelectedStatus(e.target.value),\n                                                children: statuses.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: status,\n                                                        children: status === 'all' ? 'All Statuses' : status\n                                                    }, status, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, this),\n                    selectedProducts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().bulkActions),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().selectedCount),\n                                children: [\n                                    selectedProducts.length,\n                                    \" selected\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().bulkDeleteButton),\n                                onClick: handleBulkDelete,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Delete Selected\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 302,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().tableContainer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().table),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedProducts.length === filteredProducts.length && filteredProducts.length > 0,\n                                                onChange: handleSelectAll\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Product\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Category\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Price\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Stock\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Updated\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: selectedProducts.includes(product.id),\n                                                    onChange: ()=>handleSelectProduct(product.id)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 395,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productCell),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productImage),\n                                                            children: product.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: product.imageUrl,\n                                                                alt: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().imagePlaceholder),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productInfo),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productName),\n                                                                    children: product.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productDescription),\n                                                                    children: product.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 415,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().category),\n                                                    children: product.category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().price),\n                                                    children: [\n                                                        \"$\",\n                                                        product.price.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().stock), \" \").concat(product.stock === 0 ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().outOfStock) : ''),\n                                                    children: product.stock\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().status), \" \").concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default())[product.status]),\n                                                    children: product.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().date),\n                                                    children: new Date(product.updatedAt).toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actions),\n                                                    children: [\n                                                        hasPermission('products', 'read') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                                            title: \"View\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        hasPermission('products', 'update') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                                            title: \"Edit\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        hasPermission('products', 'delete') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                                            title: \"Delete\",\n                                                            onClick: ()=>handleDeleteProduct(product.id),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                                            title: \"More\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, product.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 9\n                    }, this),\n                    filteredProducts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().emptyState),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().emptyIcon),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"No products found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Try adjusting your search or filter criteria\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 472,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this),\n            showAddProductModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modal),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalContent),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalHeader),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    children: \"Add New Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().closeButton),\n                                    onClick: ()=>setShowAddProductModal(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 486,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalBody),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Add Product form will be implemented here.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"This will include fields for:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Product name and description\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Category and pricing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Inventory management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Image uploads\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"SEO settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalActions),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryButton),\n                                    onClick: ()=>setShowAddProductModal(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().primaryButton),\n                                    children: \"Create Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 485,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 484,\n                columnNumber: 9\n            }, this),\n            showImportModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modal),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalContent),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalHeader),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    children: \"Import Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().closeButton),\n                                    onClick: ()=>setShowImportModal(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalBody),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Import products from CSV file.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().uploadArea),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: 48\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Drag and drop your CSV file here, or click to browse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"file\",\n                                            accept: \".csv\",\n                                            style: {\n                                                display: 'none'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().importInstructions),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"CSV Format Requirements:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Name, Category, Price, Stock, Status columns required\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 544,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Price should be numeric (e.g., 29.99)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Stock should be integer (e.g., 100)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Status should be: active, inactive, or draft\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 534,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalActions),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryButton),\n                                    onClick: ()=>setShowImportModal(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().primaryButton),\n                                    children: \"Import Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 524,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 523,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n        lineNumber: 275,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsManagement, \"Sd8k+dtmGd1AIybCnWSGiosquAY=\", false, function() {\n    return [\n        _contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__.useAdmin\n    ];\n});\n_c = ProductsManagement;\nvar _c;\n$RefreshReg$(_c, \"ProductsManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/products/page.tsx\n"));

/***/ })

});