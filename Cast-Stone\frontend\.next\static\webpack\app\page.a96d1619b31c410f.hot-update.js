"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!*************************************************!*\
  !*** ./src/components/home/<USER>
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CollectionsGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CollectionsGrid.module.css */ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction CollectionsGrid(param) {\n    let { categories, className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"\".concat((_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionsSection), \" \").concat(className || ''),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionsGrid),\n            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionCard),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionImage),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                src: category.image,\n                                alt: category.title,\n                                fill: true,\n                                className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionImg)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionOverlay),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionBrand),\n                                    children: category.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionSubtitle),\n                                    children: category.subtitle\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionDescription),\n                                    children: category.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionPrice),\n                                    children: category.price\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().collectionButtons),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().buildBtn),\n                                            children: \"BUILD YOURS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: (_CollectionsGrid_module_css__WEBPACK_IMPORTED_MODULE_2___default().allModelsBtn),\n                                            children: \"ALL MODELS\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, category.id, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\components\\\\home\\\\CollectionsGrid.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_c = CollectionsGrid;\nvar _c;\n$RefreshReg$(_c, \"CollectionsGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});