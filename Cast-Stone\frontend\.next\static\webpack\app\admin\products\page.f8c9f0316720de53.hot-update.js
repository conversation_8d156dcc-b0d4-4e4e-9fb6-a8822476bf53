"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/page",{

/***/ "(app-pages-browser)/./src/app/admin/products/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/admin/products/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductsManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contexts/AdminContext */ \"(app-pages-browser)/./src/contexts/AdminContext.tsx\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./page.module.css */ \"(app-pages-browser)/./src/app/admin/products/page.module.css\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_page_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* eslint-disable @next/next/no-img-element */ /* eslint-disable @typescript-eslint/no-unused-vars */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ProductsManagement() {\n    _s();\n    const { hasPermission, addNotification } = (0,_contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__.useAdmin)();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddProductModal, setShowAddProductModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showImportModal, setShowImportModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Mock data - replace with actual API calls\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsManagement.useEffect\": ()=>{\n            const fetchProducts = {\n                \"ProductsManagement.useEffect.fetchProducts\": async ()=>{\n                    try {\n                        // Simulate API call\n                        await new Promise({\n                            \"ProductsManagement.useEffect.fetchProducts\": (resolve)=>setTimeout(resolve, 1000)\n                        }[\"ProductsManagement.useEffect.fetchProducts\"]);\n                        setProducts([\n                            {\n                                id: '1',\n                                name: 'Classic Fireplace Mantel',\n                                category: 'fireplaces',\n                                price: 2500,\n                                stock: 12,\n                                status: 'active',\n                                imageUrl: '/images/fireplace-collection.jpg',\n                                description: 'Handcrafted traditional mantel with intricate detailing',\n                                createdAt: '2024-01-10',\n                                updatedAt: '2024-01-15'\n                            },\n                            {\n                                id: '2',\n                                name: 'Modern Fireplace Surround',\n                                category: 'fireplaces',\n                                price: 3200,\n                                stock: 8,\n                                status: 'active',\n                                imageUrl: '/images/fireplace-collection.jpg',\n                                description: 'Contemporary design with clean lines',\n                                createdAt: '2024-01-12',\n                                updatedAt: '2024-01-14'\n                            },\n                            {\n                                id: '3',\n                                name: 'Garden Fountain',\n                                category: 'garden',\n                                price: 1800,\n                                stock: 5,\n                                status: 'active',\n                                imageUrl: '/images/garden-collection.jpg',\n                                description: 'Three-tier fountain perfect for outdoor spaces',\n                                createdAt: '2024-01-08',\n                                updatedAt: '2024-01-13'\n                            },\n                            {\n                                id: '4',\n                                name: 'Decorative Columns',\n                                category: 'architectural',\n                                price: 1200,\n                                stock: 0,\n                                status: 'inactive',\n                                imageUrl: '/images/architectural-collection.jpg',\n                                description: 'Corinthian style columns for grand entrances',\n                                createdAt: '2024-01-05',\n                                updatedAt: '2024-01-10'\n                            }\n                        ]);\n                    } catch (error) {\n                        console.error('Failed to fetch products:', error);\n                        addNotification({\n                            type: 'error',\n                            title: 'Error',\n                            message: 'Failed to load products'\n                        });\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"ProductsManagement.useEffect.fetchProducts\"];\n            fetchProducts();\n        }\n    }[\"ProductsManagement.useEffect\"], [\n        addNotification\n    ]);\n    const categories = [\n        'all',\n        'fireplaces',\n        'garden',\n        'architectural',\n        'decorative'\n    ];\n    const statuses = [\n        'all',\n        'active',\n        'inactive',\n        'draft'\n    ];\n    const filteredProducts = products.filter((product)=>{\n        const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.description.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n        const matchesStatus = selectedStatus === 'all' || product.status === selectedStatus;\n        return matchesSearch && matchesCategory && matchesStatus;\n    });\n    const handleSelectProduct = (productId)=>{\n        setSelectedProducts((prev)=>prev.includes(productId) ? prev.filter((id)=>id !== productId) : [\n                ...prev,\n                productId\n            ]);\n    };\n    const handleSelectAll = ()=>{\n        if (selectedProducts.length === filteredProducts.length) {\n            setSelectedProducts([]);\n        } else {\n            setSelectedProducts(filteredProducts.map((p)=>p.id));\n        }\n    };\n    const handleDeleteProduct = async (productId)=>{\n        if (!hasPermission('products', 'delete')) {\n            addNotification({\n                type: 'error',\n                title: 'Permission Denied',\n                message: 'You do not have permission to delete products'\n            });\n            return;\n        }\n        if (confirm('Are you sure you want to delete this product?')) {\n            try {\n                // Simulate API call\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                setProducts((prev)=>prev.filter((p)=>p.id !== productId));\n                addNotification({\n                    type: 'success',\n                    title: 'Product Deleted',\n                    message: 'Product has been successfully deleted'\n                });\n            } catch (error) {\n                addNotification({\n                    type: 'error',\n                    title: 'Error',\n                    message: 'Failed to delete product'\n                });\n            }\n        }\n    };\n    const handleBulkDelete = async ()=>{\n        if (!hasPermission('products', 'delete')) {\n            addNotification({\n                type: 'error',\n                title: 'Permission Denied',\n                message: 'You do not have permission to delete products'\n            });\n            return;\n        }\n        if (selectedProducts.length === 0) return;\n        if (confirm(\"Are you sure you want to delete \".concat(selectedProducts.length, \" products?\"))) {\n            try {\n                // Simulate API call\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                setProducts((prev)=>prev.filter((p)=>!selectedProducts.includes(p.id)));\n                setSelectedProducts([]);\n                addNotification({\n                    type: 'success',\n                    title: 'Products Deleted',\n                    message: \"\".concat(selectedProducts.length, \" products have been deleted\")\n                });\n            } catch (error) {\n                addNotification({\n                    type: 'error',\n                    title: 'Error',\n                    message: 'Failed to delete products'\n                });\n            }\n        }\n    };\n    const handleAddProduct = ()=>{\n        setShowAddProductModal(true);\n    };\n    const handleImport = ()=>{\n        setShowImportModal(true);\n    };\n    const handleExport = async ()=>{\n        try {\n            addNotification({\n                type: 'info',\n                title: 'Export Started',\n                message: 'Your product export is being prepared...'\n            });\n            // Simulate export process\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // Create CSV content\n            const csvContent = [\n                [\n                    'Name',\n                    'Category',\n                    'Price',\n                    'Stock',\n                    'Status'\n                ],\n                ...filteredProducts.map((product)=>[\n                        product.name,\n                        product.category,\n                        product.price.toString(),\n                        product.stock.toString(),\n                        product.status\n                    ])\n            ].map((row)=>row.join(',')).join('\\n');\n            // Create and download file\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: 'text/csv'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"products-export-\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            window.URL.revokeObjectURL(url);\n            addNotification({\n                type: 'success',\n                title: 'Export Complete',\n                message: 'Products have been exported successfully'\n            });\n        } catch (error) {\n            addNotification({\n                type: 'error',\n                title: 'Export Failed',\n                message: 'Failed to export products'\n            });\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().loading),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().spinner)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading products...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n            lineNumber: 268,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productsPage),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().title),\n                                children: \"Products Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().subtitle),\n                                children: \"Manage your product catalog, inventory, and pricing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().headerActions),\n                        children: [\n                            hasPermission('products', 'create') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().primaryButton),\n                                onClick: handleAddProduct,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Product\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryButton),\n                                onClick: handleImport,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Import\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryButton),\n                                onClick: handleExport,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().toolbar),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().searchContainer),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().searchIcon)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search products...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().searchInput)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filters),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterButton), \" \").concat(showFilters ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                                onClick: ()=>setShowFilters(!showFilters),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Filters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, this),\n                            showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterDropdown),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterGroup),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedCategory,\n                                                onChange: (e)=>setSelectedCategory(e.target.value),\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: category,\n                                                        children: category === 'all' ? 'All Categories' : category\n                                                    }, category, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterGroup),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedStatus,\n                                                onChange: (e)=>setSelectedStatus(e.target.value),\n                                                children: statuses.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: status,\n                                                        children: status === 'all' ? 'All Statuses' : status\n                                                    }, status, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, this),\n                    selectedProducts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().bulkActions),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().selectedCount),\n                                children: [\n                                    selectedProducts.length,\n                                    \" selected\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().bulkDeleteButton),\n                                onClick: handleBulkDelete,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Delete Selected\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().tableContainer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().table),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedProducts.length === filteredProducts.length && filteredProducts.length > 0,\n                                                onChange: handleSelectAll\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Product\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Category\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Price\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Stock\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Updated\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: selectedProducts.includes(product.id),\n                                                    onChange: ()=>handleSelectProduct(product.id)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productCell),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productImage),\n                                                            children: product.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: product.imageUrl,\n                                                                alt: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().imagePlaceholder),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productInfo),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productName),\n                                                                    children: product.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 415,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productDescription),\n                                                                    children: product.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().category),\n                                                    children: product.category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().price),\n                                                    children: [\n                                                        \"$\",\n                                                        product.price.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().stock), \" \").concat(product.stock === 0 ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().outOfStock) : ''),\n                                                    children: product.stock\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().status), \" \").concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default())[product.status]),\n                                                    children: product.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().date),\n                                                    children: new Date(product.updatedAt).toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actions),\n                                                    children: [\n                                                        hasPermission('products', 'read') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                                            title: \"View\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        hasPermission('products', 'update') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                                            title: \"Edit\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 449,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        hasPermission('products', 'delete') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                                            title: \"Delete\",\n                                                            onClick: ()=>handleDeleteProduct(product.id),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                                            title: \"More\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, product.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 9\n                    }, this),\n                    filteredProducts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().emptyState),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().emptyIcon),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"No products found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Try adjusting your search or filter criteria\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 373,\n                columnNumber: 7\n            }, this),\n            showAddProductModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modal),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalContent),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalHeader),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    children: \"Add New Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().closeButton),\n                                    onClick: ()=>setShowAddProductModal(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalBody),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Add Product form will be implemented here.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"This will include fields for:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Product name and description\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Category and pricing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Inventory management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 502,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Image uploads\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"SEO settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalActions),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryButton),\n                                    onClick: ()=>setShowAddProductModal(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().primaryButton),\n                                    children: \"Create Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 507,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 486,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 485,\n                columnNumber: 9\n            }, this),\n            showImportModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modal),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalContent),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalHeader),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    children: \"Import Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().closeButton),\n                                    onClick: ()=>setShowImportModal(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalBody),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Import products from CSV file.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().uploadArea),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: 48\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Drag and drop your CSV file here, or click to browse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"file\",\n                                            accept: \".csv\",\n                                            style: {\n                                                display: 'none'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 540,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().importInstructions),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"CSV Format Requirements:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 543,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Name, Category, Price, Stock, Status columns required\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Price should be numeric (e.g., 29.99)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Stock should be integer (e.g., 100)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Status should be: active, inactive, or draft\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalActions),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryButton),\n                                    onClick: ()=>setShowImportModal(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().primaryButton),\n                                    children: \"Import Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 552,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 525,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 524,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n        lineNumber: 276,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsManagement, \"Sd8k+dtmGd1AIybCnWSGiosquAY=\", false, function() {\n    return [\n        _contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__.useAdmin\n    ];\n});\n_c = ProductsManagement;\nvar _c;\n$RefreshReg$(_c, \"ProductsManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/products/page.tsx\n"));

/***/ })

});