.collectionsPage {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--color-border);
}

.title {
  font-size: 32px;
  font-weight: 700;
  color: var(--color-gray-900);
  margin: 0 0 8px 0;
}

.subtitle {
  font-size: 16px;
  color: var(--color-gray-600);
  margin: 0;
}

.headerActions {
  display: flex;
  gap: 12px;
}

.primaryButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.primaryButton:hover {
  background: var(--color-primary-dark);
}

.secondaryButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: transparent;
  color: var(--color-gray-700);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.secondaryButton:hover {
  background: var(--color-gray-50);
  border-color: var(--color-gray-300);
}

.content {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 32px;
  height: calc(100vh - 200px);
}

.sidebar {
  background: var(--color-white);
  border-radius: 12px;
  border: 1px solid var(--color-border);
  overflow: hidden;
}

.mainContent {
  background: var(--color-white);
  border-radius: 12px;
  border: 1px solid var(--color-border);
  overflow: hidden;
}

.collectionDetails {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detailsHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-gray-50);
}

.collectionInfo h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 8px 0;
}

.collectionMeta {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.metaItem {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: var(--color-gray-600);
}

.statusBadge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statusBadge.published {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.statusBadge.draft {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.detailsActions {
  display: flex;
  gap: 12px;
}

.detailsContent {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.descriptionSection,
.statsSection,
.childrenSection {
  margin-bottom: 32px;
}

.descriptionSection h3,
.statsSection h3,
.childrenSection h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 16px 0;
}

.descriptionSection p {
  font-size: 14px;
  color: var(--color-gray-700);
  line-height: 1.6;
  margin: 0;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.statCard {
  padding: 20px;
  background: var(--color-gray-50);
  border-radius: 8px;
  text-align: center;
}

.statValue {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: 4px;
}

.statLabel {
  font-size: 12px;
  color: var(--color-gray-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.childrenGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.childCard {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--color-gray-50);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.childCard:hover {
  background: var(--color-gray-100);
  border-color: var(--color-primary);
}

.childIcon {
  color: var(--color-gray-600);
  display: flex;
  align-items: center;
}

.childInfo {
  flex: 1;
}

.childInfo h4 {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-gray-900);
  margin: 0 0 4px 0;
}

.childInfo p {
  font-size: 12px;
  color: var(--color-gray-600);
  margin: 0;
}

.childStatus {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-gray-600);
  text-align: center;
  padding: 40px;
}

.emptyState svg {
  color: var(--color-gray-400);
  margin-bottom: 16px;
}

.emptyState h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-gray-700);
  margin: 0 0 8px 0;
}

.emptyState p {
  font-size: 14px;
  color: var(--color-gray-600);
  margin: 0;
  max-width: 400px;
}

/* Responsive design */
@media (max-width: 1024px) {
  .content {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .sidebar {
    order: 2;
  }
  
  .mainContent {
    order: 1;
  }
}

@media (max-width: 768px) {
  .collectionsPage {
    padding: 16px;
  }
  
  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .title {
    font-size: 24px;
  }
  
  .content {
    height: auto;
    min-height: 600px;
  }
  
  .detailsHeader {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .detailsActions {
    justify-content: stretch;
  }
  
  .detailsActions button {
    flex: 1;
  }
  
  .statsGrid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
  
  .childrenGrid {
    grid-template-columns: 1fr;
  }
}
