"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/collections/page",{

/***/ "(app-pages-browser)/./src/services/api.ts":
/*!*****************************!*\
  !*** ./src/services/api.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError),\n/* harmony export */   addToOfflineQueue: () => (/* binding */ addToOfflineQueue),\n/* harmony export */   cartApi: () => (/* binding */ cartApi),\n/* harmony export */   collectionsApi: () => (/* binding */ collectionsApi),\n/* harmony export */   offlineQueue: () => (/* binding */ offlineQueue),\n/* harmony export */   ordersApi: () => (/* binding */ ordersApi),\n/* harmony export */   processOfflineQueue: () => (/* binding */ processOfflineQueue),\n/* harmony export */   productsApi: () => (/* binding */ productsApi),\n/* harmony export */   retryRequest: () => (/* binding */ retryRequest)\n/* harmony export */ });\n/* eslint-disable @typescript-eslint/no-unused-vars */ /* eslint-disable @typescript-eslint/no-explicit-any */ const API_BASE_URL = 'http://localhost:5000/api';\n// Error handling utility\nclass ApiError extends Error {\n    constructor(message, status, code){\n        super(message), this.status = status, this.code = code;\n        this.name = 'ApiError';\n    }\n}\n// Request utility with error handling\nasync function apiRequest(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const url = \"\".concat(API_BASE_URL).concat(endpoint);\n    // Get auth token from localStorage\n    const token =  true ? localStorage.getItem('authToken') : 0;\n    const defaultHeaders = {\n        'Content-Type': 'application/json'\n    };\n    if (token) {\n        defaultHeaders.Authorization = \"Bearer \".concat(token);\n    }\n    const config = {\n        ...options,\n        headers: {\n            ...defaultHeaders,\n            ...options.headers\n        }\n    };\n    try {\n        const response = await fetch(url, config);\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new ApiError(errorData.message || \"HTTP \".concat(response.status, \": \").concat(response.statusText), response.status, errorData.code);\n        }\n        const data = await response.json();\n        if (!data.success) {\n            throw new ApiError(data.message || 'API request failed', undefined, data.code);\n        }\n        return data;\n    } catch (error) {\n        if (error instanceof ApiError) {\n            throw error;\n        }\n        // Network or other errors\n        if (error instanceof TypeError && error.message.includes('fetch')) {\n            throw new ApiError('Network error. Please check your connection.', 0, 'NETWORK_ERROR');\n        }\n        throw new ApiError('An unexpected error occurred.', 0, 'UNKNOWN_ERROR');\n    }\n}\n// Cart API\nconst cartApi = {\n    // Get user's cart\n    async getCart () {\n        return apiRequest('/cart');\n    },\n    // Add item to cart\n    async addToCart (productId) {\n        let quantity = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1;\n        return apiRequest('/cart/add', {\n            method: 'POST',\n            body: JSON.stringify({\n                productId,\n                quantity\n            })\n        });\n    },\n    // Update item quantity\n    async updateCartItem (productId, quantity) {\n        return apiRequest(\"/cart/item/\".concat(productId), {\n            method: 'PUT',\n            body: JSON.stringify({\n                quantity\n            })\n        });\n    },\n    // Remove item from cart\n    async removeFromCart (productId) {\n        return apiRequest(\"/cart/item/\".concat(productId), {\n            method: 'DELETE'\n        });\n    },\n    // Clear entire cart\n    async clearCart () {\n        return apiRequest('/cart/clear', {\n            method: 'DELETE'\n        });\n    },\n    // Sync cart with frontend\n    async syncCart (items) {\n        return apiRequest('/cart/sync', {\n            method: 'POST',\n            body: JSON.stringify({\n                items\n            })\n        });\n    }\n};\n// Orders API\nconst ordersApi = {\n    // Create payment intent\n    async createPaymentIntent (amount) {\n        const response = await apiRequest('/orders/payment-intent', {\n            method: 'POST',\n            body: JSON.stringify({\n                amount\n            })\n        });\n        return response;\n    },\n    // Create order after successful payment\n    async createOrder (paymentIntentId, shippingAddress, paymentMethod) {\n        return apiRequest('/orders/create', {\n            method: 'POST',\n            body: JSON.stringify({\n                paymentIntentId,\n                shippingAddress,\n                paymentMethod\n            })\n        });\n    },\n    // Get user's orders\n    async getUserOrders () {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10;\n        return apiRequest(\"/orders?page=\".concat(page, \"&limit=\").concat(limit));\n    },\n    // Get specific order\n    async getOrder (orderNumber) {\n        return apiRequest(\"/orders/\".concat(orderNumber));\n    },\n    // Handle payment failure\n    async handlePaymentFailure (paymentIntentId, error) {\n        return apiRequest('/orders/payment-failure', {\n            method: 'POST',\n            body: JSON.stringify({\n                paymentIntentId,\n                error\n            })\n        });\n    }\n};\n// Products API (if needed for cart integration)\nconst productsApi = {\n    // Get all products\n    async getProducts (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.category) searchParams.append('category', params.category);\n        if (params === null || params === void 0 ? void 0 : params.tags) searchParams.append('tags', params.tags);\n        if (params === null || params === void 0 ? void 0 : params.collectionId) searchParams.append('collectionId', params.collectionId);\n        if (params === null || params === void 0 ? void 0 : params.collectionPath) searchParams.append('collectionPath', params.collectionPath);\n        if (params === null || params === void 0 ? void 0 : params.includeDescendants) searchParams.append('includeDescendants', params.includeDescendants.toString());\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.sortBy) searchParams.append('sortBy', params.sortBy);\n        if (params === null || params === void 0 ? void 0 : params.sortOrder) searchParams.append('sortOrder', params.sortOrder);\n        const queryString = searchParams.toString();\n        const endpoint = queryString ? \"/products?\".concat(queryString) : '/products';\n        return apiRequest(endpoint);\n    },\n    // Get products by collection path\n    async getProductsByCollectionPath (path, params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.sortBy) searchParams.append('sortBy', params.sortBy);\n        if (params === null || params === void 0 ? void 0 : params.sortOrder) searchParams.append('sortOrder', params.sortOrder);\n        if (params === null || params === void 0 ? void 0 : params.includeDescendants) searchParams.append('includeDescendants', params.includeDescendants.toString());\n        const queryString = searchParams.toString();\n        const endpoint = queryString ? \"/products/collection/\".concat(path, \"?\").concat(queryString) : \"/products/collection/\".concat(path);\n        return apiRequest(endpoint);\n    },\n    // Get single product\n    async getProduct (id) {\n        return apiRequest(\"/products/\".concat(id));\n    }\n};\n// Collections API for hierarchical collections\nconst collectionsApi = {\n    // Get all collections with hierarchy support\n    async getCollections (params) {\n        const searchParams = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.page) searchParams.append('page', params.page.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) searchParams.append('limit', params.limit.toString());\n        if ((params === null || params === void 0 ? void 0 : params.level) !== undefined) searchParams.append('level', params.level.toString());\n        if (params === null || params === void 0 ? void 0 : params.parentId) searchParams.append('parentId', params.parentId);\n        if (params === null || params === void 0 ? void 0 : params.hierarchy) searchParams.append('hierarchy', params.hierarchy.toString());\n        if ((params === null || params === void 0 ? void 0 : params.published) !== undefined) searchParams.append('published', params.published.toString());\n        const queryString = searchParams.toString();\n        const endpoint = queryString ? \"/collections?\".concat(queryString) : '/collections';\n        return apiRequest(endpoint);\n    },\n    // Get collection hierarchy\n    async getCollectionHierarchy () {\n        return apiRequest('/collections?hierarchy=true');\n    },\n    // Get collections by level\n    async getCollectionsByLevel (level) {\n        return apiRequest(\"/collections?level=\".concat(level));\n    },\n    // Get root collections\n    async getRootCollections () {\n        return apiRequest('/collections?level=0&published=true');\n    },\n    // Get single collection\n    async getCollection (id) {\n        return apiRequest(\"/collections/\".concat(id));\n    },\n    // Get collection by path\n    async getCollectionByPath (path) {\n        return apiRequest(\"/collections/path/\".concat(path));\n    },\n    // Get collection breadcrumbs\n    async getCollectionBreadcrumbs (id) {\n        return apiRequest(\"/collections/\".concat(id, \"/breadcrumbs\"));\n    }\n};\n// Retry utility for failed requests\nconst retryRequest = async function(requestFn) {\n    let maxRetries = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3, delay = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1000;\n    let lastError;\n    for(let i = 0; i <= maxRetries; i++){\n        try {\n            return await requestFn();\n        } catch (error) {\n            lastError = error;\n            // Don't retry on client errors (4xx) except 408, 429\n            if (error instanceof ApiError && error.status) {\n                if (error.status >= 400 && error.status < 500 && error.status !== 408 && error.status !== 429) {\n                    throw error;\n                }\n            }\n            if (i < maxRetries) {\n                await new Promise((resolve)=>setTimeout(resolve, delay * Math.pow(2, i)));\n            }\n        }\n    }\n    throw lastError;\n};\n// Offline detection and queue\nconst offlineQueue = [];\nconst addToOfflineQueue = (requestFn)=>{\n    offlineQueue.push(requestFn);\n};\nconst processOfflineQueue = async ()=>{\n    if (!navigator.onLine || offlineQueue.length === 0) return;\n    const requests = [\n        ...offlineQueue\n    ];\n    offlineQueue.length = 0; // Clear the queue\n    for (const request of requests){\n        try {\n            await request();\n        } catch (error) {\n            console.error('Failed to process offline request:', error);\n            // Re-add to queue if it fails\n            offlineQueue.push(request);\n        }\n    }\n};\n// Listen for online events to process queue\nif (true) {\n    window.addEventListener('online', processOfflineQueue);\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/api.ts\n"));

/***/ })

});