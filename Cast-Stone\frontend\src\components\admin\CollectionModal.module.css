.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modalContainer {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid var(--cart-border);
  background: var(--color-gray-50);
}

.modalHeader h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--color-gray-900);
}

.closeButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  color: var(--color-gray-600);
  transition: all 0.2s;
}

.closeButton:hover {
  background: var(--color-gray-200);
  color: var(--color-gray-800);
}

.modalForm {
  padding: 24px;
  max-height: calc(90vh - 120px);
  overflow-y: auto;
}

.formGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
}

.formSection {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.formSection h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-gray-900);
  padding-bottom: 8px;
  border-bottom: 1px solid var(--color-border);
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.formGroup label {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-gray-700);
}

.formGroup input,
.formGroup select,
.formGroup textarea {
  padding: 10px 12px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.formGroup input:focus,
.formGroup select:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.formGroup textarea {
  resize: vertical;
  min-height: 80px;
}

.formGroup small {
  font-size: 12px;
  color: var(--color-gray-600);
}

.inputError {
  border-color: var(--color-error) !important;
  box-shadow: 0 0 0 3px var(--color-error-light) !important;
}

.errorText {
  font-size: 12px;
  color: var(--color-error);
  margin-top: 4px;
}

.checkboxLabel {
  display: flex !important;
  flex-direction: row !important;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkboxLabel input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.checkboxLabel span {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-gray-700);
}

.uploadArea {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  border: 2px dashed var(--color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  color: var(--color-gray-600);
}

.uploadArea:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.uploadArea p {
  margin: 8px 0 4px 0;
  font-weight: 500;
}

.uploadArea small {
  color: var(--color-gray-500);
}

.submitError {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--color-error-light);
  color: var(--color-error-dark);
  border-radius: 6px;
  font-size: 14px;
  margin-top: 20px;
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border);
}

.cancelButton {
  padding: 10px 20px;
  background: transparent;
  color: var(--color-gray-700);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.cancelButton:hover {
  background: var(--color-gray-50);
  border-color: var(--color-gray-300);
}

.saveButton {
  padding: 10px 20px;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.saveButton:hover:not(:disabled) {
  background: var(--color-primary-dark);
}

.saveButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive design */
@media (max-width: 768px) {
  .modalOverlay {
    padding: 10px;
  }
  
  .modalContainer {
    max-height: 95vh;
  }
  
  .modalHeader {
    padding: 16px;
  }
  
  .modalForm {
    padding: 16px;
  }
  
  .formGrid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .modalActions {
    flex-direction: column-reverse;
    gap: 8px;
  }
  
  .cancelButton,
  .saveButton {
    width: 100%;
    padding: 12px;
  }
}
