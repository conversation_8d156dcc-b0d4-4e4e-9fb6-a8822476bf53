/* Top Navigation Bar */
.navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(139, 69, 19, 0.1);
}

 /* .navContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem; 
  display: flex;
  justify-content: space-between;
  flex-direction: column; 
    justify-content: center;
  align-items: center;
}  */

.navContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  flex-direction: row;
  justify-content: space-between; /* Spread logo and nav items */
  align-items: center;
}

.navMenuWrapper {
  flex: 1;
  display: flex;
  justify-content: center;
}

.cartWrapper {
  display: flex;
  align-items: center;
  margin-left: 1rem;
}

.logo h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  letter-spacing: -0.02em;
}

.logo span {
  display: block;
  font-size: 0.9rem;
  color: var(--text-light);
  font-weight: 400;
}

.navMenu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
  align-items: center;
}

.navMenu > li {
  position: relative;
}

.navMenu a {
  text-decoration: none;
  color: var(--text-dark);
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
  padding: 0.5rem 0;
}

.navMenu a:hover {
  color: var(--primary-color);
}

/* Dropdown Styles */
.dropdown {
  position: relative;
}

.dropdownToggle::after {
  content: '▼';
  font-size: 0.7rem;
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

.dropdown:hover .dropdownToggle::after {
  transform: rotate(180deg);
}

.dropdownMenu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  min-width: 200px;
  box-shadow: var(--shadow);
  border-radius: 8px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  list-style: none;
  margin: 0;
}

.dropdown:hover .dropdownMenu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdownMenu li {
  margin: 0;
}

.dropdownMenu a {
  display: block;
  padding: 0.75rem 1.5rem;
  color: var(--text-dark);
  text-decoration: none;
  transition: background-color 0.3s ease;
  font-weight: 400;
}

.dropdownMenu a:hover {
  background-color: var(--background-light);
  color: var(--primary-color);
}

/* Mega Menu Styles */
.megaDropdown {
  position: relative;
}

.megaMenu {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  width: 100vw;
  max-width: 1400px;
  background: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border-top: 3px solid var(--primary-color);
  z-index: 1000;
  padding: 2rem 0;
  border-radius: 0 0 12px 12px;
}

.megaMenuContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 3fr 1fr;
  gap: 3rem;
}

.megaMenuSection h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--secondary-color);
}

.collectionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2.5rem;
  margin-bottom: 1rem;
}

.collectionGroup {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--background-light);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.collectionGroup:hover {
  background: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.collectionTitle {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.2s ease;
  padding: 0.5rem 0;
  border-bottom: 2px solid transparent;
  display: inline-block;
}

.collectionTitle:hover {
  color: var(--cart-primary-hover);
  border-bottom-color: var(--accent-color);
}

.subCollectionsList {
  list-style: none;
  margin: 0.75rem 0 0 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.subCollectionsList a {
  font-size: 0.9rem;
  color: var(--text-light);
  text-decoration: none;
  padding: 0.375rem 0.75rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  position: relative;
  padding-left: 1.5rem;
}

.subCollectionsList a:before {
  content: '→';
  position: absolute;
  left: 0.5rem;
  color: var(--accent-color);
  font-weight: bold;
}

.subCollectionsList a:hover {
  color: var(--primary-color);
  background: rgba(139, 69, 19, 0.1);
  transform: translateX(4px);
}

.subSubCollectionsList {
  list-style: none;
  margin: 0.5rem 0 0 1.5rem;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.subSubCollectionsList a {
  font-size: 0.8rem;
  color: var(--text-light);
  text-decoration: none;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
  transition: all 0.2s ease;
  position: relative;
  padding-left: 1.25rem;
}

.subSubCollectionsList a:before {
  content: '•';
  position: absolute;
  left: 0.5rem;
  color: var(--accent-color);
}

.subSubCollectionsList a:hover {
  color: var(--accent-color);
  background: rgba(205, 133, 63, 0.1);
}

.quickLinks {
  list-style: none;
  margin: 0;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  background: var(--background-light);
  border-radius: 8px;
}

.quickLinks a {
  font-size: 0.95rem;
  color: var(--text-dark);
  text-decoration: none;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  transition: all 0.2s ease;
  border: 1px solid var(--cart-border);
  background: white;
  font-weight: 500;
  text-align: center;
}

.quickLinks a:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(139, 69, 19, 0.3);
}

/* Mobile Hamburger Menu */
.mobileMenuToggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
  z-index: 1001;
}

.hamburgerLine {
  width: 25px;
  height: 3px;
  background-color: var(--text-dark);
  margin: 3px 0;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.mobileMenuToggle.active .hamburgerLine:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobileMenuToggle.active .hamburgerLine:nth-child(2) {
  opacity: 0;
}

.mobileMenuToggle.active .hamburgerLine:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.mobileMenu {
  position: fixed;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100vh;
  background: white;
  z-index: 999;
  transition: left 0.3s ease;
  padding-top: 80px;
  overflow-y: auto;
}

.mobileMenu.active {
  left: 0;
}

.mobileNavMenu {
  list-style: none;
  padding: 2rem;
  margin: 0;
}

.mobileNavMenu > li {
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--background-light);
  padding-bottom: 1rem;
}

.mobileNavMenu a {
  display: block;
  padding: 1rem 0;
  color: var(--text-dark);
  text-decoration: none;
  font-size: 1.2rem;
  font-weight: 500;
}

.mobileDropdownToggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.mobileDropdownToggle::after {
  content: '+';
  font-size: 1.5rem;
  transition: transform 0.3s ease;
}

.mobileDropdownToggle.active::after {
  transform: rotate(45deg);
}

.mobileDropdownMenu {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
  background: var(--background-light);
  margin-top: 1rem;
  border-radius: 8px;
}

.mobileDropdownMenu.active {
  max-height: 300px;
}

.mobileDropdownMenu li {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.mobileDropdownMenu a {
  padding: 0.75rem 1rem;
  font-size: 1rem;
  color: var(--text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
  .navMenu {
    display: none;
  }

  .mobileMenuToggle {
    display: flex;
  }

  .navContainer {
    padding: 0 1rem;
  }

  /* Hide mega menu on mobile */
  .megaMenu {
    display: none;
  }
}

@media (max-width: 480px) {
  .navMenu {
    display: none;
  }
}

/* Large screens - improve mega menu layout */
@media (min-width: 1200px) {
  .megaMenu {
    max-width: 1600px;
  }

  .collectionsGrid {
    grid-template-columns: repeat(4, 1fr);
  }
}
