"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./src/app/products/page.tsx":
/*!***********************************!*\
  !*** ./src/app/products/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Products)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components */ \"(app-pages-browser)/./src/components/index.ts\");\n/* harmony import */ var _services_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../services/api */ \"(app-pages-browser)/./src/services/api.ts\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./page.module.css */ \"(app-pages-browser)/./src/app/products/page.module.css\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_page_module_css__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Products() {\n    _s();\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const categories = [\n        {\n            id: 'all',\n            name: 'All Products'\n        },\n        {\n            id: 'fireplaces',\n            name: 'Fireplaces'\n        },\n        {\n            id: 'garden',\n            name: 'Garden Features'\n        },\n        {\n            id: 'architectural',\n            name: 'Architectural'\n        },\n        {\n            id: 'decorative',\n            name: 'Decorative'\n        }\n    ];\n    // Fetch products from API\n    const fetchProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Products.useCallback[fetchProducts]\": async ()=>{\n            try {\n                console.log('Fetching products...', {\n                    selectedCategory\n                });\n                setIsLoading(true);\n                setError(null);\n                const response = await _services_api__WEBPACK_IMPORTED_MODULE_4__.productsApi.getProducts({\n                    category: selectedCategory === 'all' ? undefined : selectedCategory,\n                    limit: 50\n                });\n                console.log('Products API response:', response);\n                if (response.products) {\n                    // Transform API data to match our interface\n                    const transformedProducts = response.products.map({\n                        \"Products.useCallback[fetchProducts].transformedProducts\": (product)=>{\n                            var _product_priceRange, _product_featuredImage, _product_images_, _product_images;\n                            return {\n                                id: product._id || product.id,\n                                name: product.title || product.name,\n                                category: product.category,\n                                price: ((_product_priceRange = product.priceRange) === null || _product_priceRange === void 0 ? void 0 : _product_priceRange.min) || product.price || 0,\n                                image: ((_product_featuredImage = product.featuredImage) === null || _product_featuredImage === void 0 ? void 0 : _product_featuredImage.url) || ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : (_product_images_ = _product_images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.url) || product.image || '/images/placeholder.jpg',\n                                description: product.description || ''\n                            };\n                        }\n                    }[\"Products.useCallback[fetchProducts].transformedProducts\"]);\n                    setProducts(transformedProducts);\n                }\n            } catch (error) {\n                console.error('Failed to fetch products:', error);\n                setError('Failed to load products. Please try again.');\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"Products.useCallback[fetchProducts]\"], [\n        selectedCategory\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Products.useEffect\": ()=>{\n            fetchProducts();\n        }\n    }[\"Products.useEffect\"], [\n        fetchProducts\n    ]);\n    const filteredProducts = products; // Filtering is now done in the API call\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().container),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_3__.Navigation, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().hero),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().heroContent),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().heroTitle),\n                            children: \"Our Products\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().heroSubtitle),\n                            children: \"Discover our complete collection of handcrafted cast stone elements, from elegant fireplaces to stunning garden features.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().productsSection),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().categoryFilter),\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().categoryBtn), \" \").concat(selectedCategory === category.id ? (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().active) : ''),\n                                onClick: ()=>setSelectedCategory(category.id),\n                                children: category.name\n                            }, category.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().productsGrid),\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().loadingContainer),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().spinner)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Loading products...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().errorContainer),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().errorMessage),\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: fetchProducts,\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().retryButton),\n                                    children: \"Try Again\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 13\n                        }, this) : filteredProducts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().emptyContainer),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No products found in this category.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, this) : filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().productCard),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().productImageContainer),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                src: product.image,\n                                                alt: product.name,\n                                                width: 400,\n                                                height: 300,\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().productImage)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().productOverlay),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().viewBtn),\n                                                        children: \"View Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_3__.AddToCartButton, {\n                                                        product: {\n                                                            id: product.id,\n                                                            name: product.name,\n                                                            price: product.price,\n                                                            image: product.image,\n                                                            category: product.category,\n                                                            description: product.description\n                                                        },\n                                                        variant: \"primary\",\n                                                        size: \"medium\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().productInfo),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().productName),\n                                                children: product.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().productDescription),\n                                                children: product.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().productFooter),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().productPrice),\n                                                        children: [\n                                                            \"$\",\n                                                            product.price.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_3__.AddToCartButton, {\n                                                        product: {\n                                                            id: product.id,\n                                                            name: product.name,\n                                                            price: product.price,\n                                                            image: product.image,\n                                                            category: product.category,\n                                                            description: product.description\n                                                        },\n                                                        variant: \"outline\",\n                                                        size: \"small\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, product.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().ctaSection),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().ctaContent),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Need Custom Design?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Our master craftsmen can create bespoke cast stone pieces tailored to your specific requirements.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_5___default().ctaBtn),\n                            children: \"Request Custom Quote\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components__WEBPACK_IMPORTED_MODULE_3__.Footer, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\products\\\\page.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(Products, \"uEmSPGNyJA5S2rKpY91Ys0Dpl/o=\");\n_c = Products;\nvar _c;\n$RefreshReg$(_c, \"Products\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/products/page.tsx\n"));

/***/ })

});