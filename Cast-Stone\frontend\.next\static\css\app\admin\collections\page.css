/*!***********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/admin/CollectionHierarchy.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************/
.CollectionHierarchy_hierarchyContainer__lz8mn {
  background: white;
  border-radius: 8px;
  border: 1px solid var(--cart-border);
  overflow: hidden;
}

.CollectionHierarchy_hierarchyHeader__Ekcmd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--cart-border);
  background: var(--background-light);
}

.CollectionHierarchy_hierarchyHeader__Ekcmd h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-dark);
}

.CollectionHierarchy_addRootButton__LUKk2 {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.CollectionHierarchy_addRootButton__LUKk2:hover {
  background: var(--cart-primary-hover);
}

.CollectionHierarchy_hierarchyTree__IWInT {
  max-height: 600px;
  overflow-y: auto;
}

.CollectionHierarchy_collectionNode__J_Ag_ {
  border-bottom: 1px solid var(--cart-border);
}

.CollectionHierarchy_collectionNode__J_Ag_:last-child {
  border-bottom: none;
}

.CollectionHierarchy_collectionItem__3nwho {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  cursor: pointer;
  transition: background-color 0.2s;
  min-height: 60px;
}

.CollectionHierarchy_collectionItem__3nwho:hover {
  background: var(--background-light);
}

.CollectionHierarchy_collectionItem__3nwho.CollectionHierarchy_selected__ExALV {
  background: var(--secondary-color);
  border-left: 3px solid var(--primary-color);
}

.CollectionHierarchy_collectionContent__ItDfW {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 8px;
}

.CollectionHierarchy_expandButton__Tcc5i {
  width: 20px;
  display: flex;
  justify-content: center;
}

.CollectionHierarchy_expandToggle__bXKWi {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  color: var(--color-gray-600);
  transition: all 0.2s;
}

.CollectionHierarchy_expandToggle__bXKWi:hover {
  background: var(--color-gray-200);
  color: var(--color-gray-800);
}

.CollectionHierarchy_expandSpacer__ueF5Q {
  width: 20px;
}

.CollectionHierarchy_collectionIcon__4adtE {
  color: var(--color-gray-600);
  display: flex;
  align-items: center;
}

.CollectionHierarchy_collectionInfo__6mUL4 {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.CollectionHierarchy_collectionTitle__Id281 {
  font-weight: 500;
  color: var(--color-gray-900);
  font-size: 14px;
}

.CollectionHierarchy_collectionMeta__sxym6 {
  font-size: 12px;
  color: var(--color-gray-600);
}

.CollectionHierarchy_collectionStatus__unblF {
  margin-right: 12px;
}

.CollectionHierarchy_statusBadge__EWdjc {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.CollectionHierarchy_statusBadge__EWdjc.CollectionHierarchy_published__JPipe {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.CollectionHierarchy_statusBadge__EWdjc.CollectionHierarchy_draft__sMVxV {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.CollectionHierarchy_collectionActions__3bLBq {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
  margin-right: 12px;
}

.CollectionHierarchy_collectionItem__3nwho:hover .CollectionHierarchy_collectionActions__3bLBq {
  opacity: 1;
}

.CollectionHierarchy_actionButton__ZLATT {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: none;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: var(--color-gray-600);
  transition: all 0.2s;
}

.CollectionHierarchy_actionButton__ZLATT:hover {
  background: var(--color-gray-200);
  color: var(--color-gray-800);
}

.CollectionHierarchy_childrenContainer__uTTfw {
  border-left: 1px solid var(--color-border-light);
  margin-left: 20px;
}

.CollectionHierarchy_loadingContainer__qlNq2 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  gap: 16px;
}

.CollectionHierarchy_spinner__6xDfJ {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: CollectionHierarchy_spin__dSZzR 1s linear infinite;
}

@keyframes CollectionHierarchy_spin__dSZzR {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.CollectionHierarchy_errorContainer__PRqOl {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  gap: 16px;
}

.CollectionHierarchy_errorMessage__RgoGB {
  color: var(--color-error);
  text-align: center;
  margin: 0;
}

.CollectionHierarchy_retryButton__jTYdl {
  padding: 8px 16px;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.CollectionHierarchy_retryButton__jTYdl:hover {
  background: var(--color-primary-dark);
}

.CollectionHierarchy_emptyState__0W_fU {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
  gap: 16px;
  color: var(--color-gray-600);
}

.CollectionHierarchy_emptyState__0W_fU svg {
  color: var(--color-gray-400);
}

.CollectionHierarchy_emptyState__0W_fU p {
  margin: 0;
  font-size: 16px;
  color: var(--color-gray-600);
}

.CollectionHierarchy_addFirstButton__4gxx9 {
  padding: 12px 24px;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.CollectionHierarchy_addFirstButton__4gxx9:hover {
  background: var(--color-primary-dark);
}

/* Responsive design */
@media (max-width: 768px) {
  .CollectionHierarchy_hierarchyHeader__Ekcmd {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .CollectionHierarchy_collectionItem__3nwho {
    padding: 8px 0;
    min-height: 50px;
  }
  
  .CollectionHierarchy_collectionActions__3bLBq {
    opacity: 1;
  }
  
  .CollectionHierarchy_actionButton__ZLATT {
    width: 32px;
    height: 32px;
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/admin/CollectionModal.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
.CollectionModal_modalOverlay___vawn {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.CollectionModal_modalContainer__4GZZw {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.CollectionModal_modalHeader__24erq {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid var(--cart-border);
  background: var(--color-gray-50);
}

.CollectionModal_modalHeader__24erq h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--color-gray-900);
}

.CollectionModal_closeButton__10tQk {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  color: var(--color-gray-600);
  transition: all 0.2s;
}

.CollectionModal_closeButton__10tQk:hover {
  background: var(--color-gray-200);
  color: var(--color-gray-800);
}

.CollectionModal_modalForm__1KZOQ {
  padding: 24px;
  max-height: calc(90vh - 120px);
  overflow-y: auto;
}

.CollectionModal_formGrid__3mmYQ {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
}

.CollectionModal_formSection__We35p {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.CollectionModal_formSection__We35p h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-gray-900);
  padding-bottom: 8px;
  border-bottom: 1px solid var(--color-border);
}

.CollectionModal_formGroup__Joi79 {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.CollectionModal_formGroup__Joi79 label {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-gray-700);
}

.CollectionModal_formGroup__Joi79 input,
.CollectionModal_formGroup__Joi79 select,
.CollectionModal_formGroup__Joi79 textarea {
  padding: 10px 12px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.CollectionModal_formGroup__Joi79 input:focus,
.CollectionModal_formGroup__Joi79 select:focus,
.CollectionModal_formGroup__Joi79 textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.CollectionModal_formGroup__Joi79 textarea {
  resize: vertical;
  min-height: 80px;
}

.CollectionModal_formGroup__Joi79 small {
  font-size: 12px;
  color: var(--color-gray-600);
}

.CollectionModal_inputError__NVeC6 {
  border-color: var(--color-error) !important;
  box-shadow: 0 0 0 3px var(--color-error-light) !important;
}

.CollectionModal_errorText__4rreW {
  font-size: 12px;
  color: var(--color-error);
  margin-top: 4px;
}

.CollectionModal_checkboxLabel__ENzz8 {
  display: flex !important;
  flex-direction: row !important;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.CollectionModal_checkboxLabel__ENzz8 input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.CollectionModal_checkboxLabel__ENzz8 span {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-gray-700);
}

.CollectionModal_uploadArea__XG9aV {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  border: 2px dashed var(--color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  color: var(--color-gray-600);
}

.CollectionModal_uploadArea__XG9aV:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.CollectionModal_uploadArea__XG9aV p {
  margin: 8px 0 4px 0;
  font-weight: 500;
}

.CollectionModal_uploadArea__XG9aV small {
  color: var(--color-gray-500);
}

.CollectionModal_submitError__Gco32 {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--color-error-light);
  color: var(--color-error-dark);
  border-radius: 6px;
  font-size: 14px;
  margin-top: 20px;
}

.CollectionModal_modalActions__qPhNr {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border);
}

.CollectionModal_cancelButton__GjBkU {
  padding: 10px 20px;
  background: transparent;
  color: var(--color-gray-700);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.CollectionModal_cancelButton__GjBkU:hover {
  background: var(--color-gray-50);
  border-color: var(--color-gray-300);
}

.CollectionModal_saveButton__4Tx_q {
  padding: 10px 20px;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.CollectionModal_saveButton__4Tx_q:hover:not(:disabled) {
  background: var(--color-primary-dark);
}

.CollectionModal_saveButton__4Tx_q:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive design */
@media (max-width: 768px) {
  .CollectionModal_modalOverlay___vawn {
    padding: 10px;
  }
  
  .CollectionModal_modalContainer__4GZZw {
    max-height: 95vh;
  }
  
  .CollectionModal_modalHeader__24erq {
    padding: 16px;
  }
  
  .CollectionModal_modalForm__1KZOQ {
    padding: 16px;
  }
  
  .CollectionModal_formGrid__3mmYQ {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .CollectionModal_modalActions__qPhNr {
    flex-direction: column-reverse;
    gap: 8px;
  }
  
  .CollectionModal_cancelButton__GjBkU,
  .CollectionModal_saveButton__4Tx_q {
    width: 100%;
    padding: 12px;
  }
}

/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/app/admin/collections/page.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
.page_collectionsPage__G6y4A {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.page_header__2FUEd {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--color-border);
}

.page_title__DrTlr {
  font-size: 32px;
  font-weight: 700;
  color: var(--color-gray-900);
  margin: 0 0 8px 0;
}

.page_subtitle__x0rin {
  font-size: 16px;
  color: var(--color-gray-600);
  margin: 0;
}

.page_headerActions__cszDS {
  display: flex;
  gap: 12px;
}

.page_primaryButton__etF9g {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.page_primaryButton__etF9g:hover {
  background: var(--color-primary-dark);
}

.page_secondaryButton__ZQjEx {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: transparent;
  color: var(--color-gray-700);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.page_secondaryButton__ZQjEx:hover {
  background: var(--color-gray-50);
  border-color: var(--color-gray-300);
}

.page_content__aFiR9 {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 32px;
  height: calc(100vh - 200px);
}

.page_sidebar__Ub1d6 {
  background: var(--color-white);
  border-radius: 12px;
  border: 1px solid var(--color-border);
  overflow: hidden;
}

.page_mainContent__serbp {
  background: var(--color-white);
  border-radius: 12px;
  border: 1px solid var(--color-border);
  overflow: hidden;
}

.page_collectionDetails__3IXqJ {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page_detailsHeader__kqpXG {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-gray-50);
}

.page_collectionInfo__DiRoi h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 8px 0;
}

.page_collectionMeta__tGpKZ {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.page_metaItem__2fgdT {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: var(--color-gray-600);
}

.page_statusBadge__OpOQo {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.page_statusBadge__OpOQo.page_published__0ZkVw {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.page_statusBadge__OpOQo.page_draft__PFOvN {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.page_detailsActions__6RkGe {
  display: flex;
  gap: 12px;
}

.page_detailsContent__xI67v {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.page_descriptionSection__uocng,
.page_statsSection__9KrIn,
.page_childrenSection__nhamY {
  margin-bottom: 32px;
}

.page_descriptionSection__uocng h3,
.page_statsSection__9KrIn h3,
.page_childrenSection__nhamY h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 16px 0;
}

.page_descriptionSection__uocng p {
  font-size: 14px;
  color: var(--color-gray-700);
  line-height: 1.6;
  margin: 0;
}

.page_statsGrid__XjzQI {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.page_statCard__ouQOq {
  padding: 20px;
  background: var(--color-gray-50);
  border-radius: 8px;
  text-align: center;
}

.page_statValue__w3FaM {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: 4px;
}

.page_statLabel__l4UhV {
  font-size: 12px;
  color: var(--color-gray-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.page_childrenGrid__wDS9i {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.page_childCard__w_aKx {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--color-gray-50);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.page_childCard__w_aKx:hover {
  background: var(--color-gray-100);
  border-color: var(--color-primary);
}

.page_childIcon___PNaD {
  color: var(--color-gray-600);
  display: flex;
  align-items: center;
}

.page_childInfo__WCctG {
  flex: 1;
}

.page_childInfo__WCctG h4 {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-gray-900);
  margin: 0 0 4px 0;
}

.page_childInfo__WCctG p {
  font-size: 12px;
  color: var(--color-gray-600);
  margin: 0;
}

.page_childStatus__5h3mu {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.page_emptyState__V0Tkm {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-gray-600);
  text-align: center;
  padding: 40px;
}

.page_emptyState__V0Tkm svg {
  color: var(--color-gray-400);
  margin-bottom: 16px;
}

.page_emptyState__V0Tkm h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-gray-700);
  margin: 0 0 8px 0;
}

.page_emptyState__V0Tkm p {
  font-size: 14px;
  color: var(--color-gray-600);
  margin: 0;
  max-width: 400px;
}

/* Responsive design */
@media (max-width: 1024px) {
  .page_content__aFiR9 {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .page_sidebar__Ub1d6 {
    order: 2;
  }
  
  .page_mainContent__serbp {
    order: 1;
  }
}

@media (max-width: 768px) {
  .page_collectionsPage__G6y4A {
    padding: 16px;
  }
  
  .page_header__2FUEd {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .page_title__DrTlr {
    font-size: 24px;
  }
  
  .page_content__aFiR9 {
    height: auto;
    min-height: 600px;
  }
  
  .page_detailsHeader__kqpXG {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .page_detailsActions__6RkGe {
    justify-content: stretch;
  }
  
  .page_detailsActions__6RkGe button {
    flex: 1;
  }
  
  .page_statsGrid__XjzQI {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
  
  .page_childrenGrid__wDS9i {
    grid-template-columns: 1fr;
  }
}

