{"c": ["app/admin/products/page", "webpack"], "r": [], "m": ["(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/download.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/ellipsis.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/funnel.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/square-pen.js", "(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/upload.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUmer%20Farooq%5C%5CDesktop%5C%5CPatricks%20web%5C%5CCast-Stone%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cadmin%5C%5Cproducts%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/admin/products/page.module.css", "(app-pages-browser)/./src/app/admin/products/page.tsx"]}