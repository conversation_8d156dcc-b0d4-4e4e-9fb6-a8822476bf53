"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/page",{

/***/ "(app-pages-browser)/./src/app/admin/products/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/admin/products/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductsManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,Eye,Filter,MoreHorizontal,Plus,Search,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/../node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contexts/AdminContext */ \"(app-pages-browser)/./src/contexts/AdminContext.tsx\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./page.module.css */ \"(app-pages-browser)/./src/app/admin/products/page.module.css\");\n/* harmony import */ var _page_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_page_module_css__WEBPACK_IMPORTED_MODULE_3__);\n/* eslint-disable @next/next/no-img-element */ /* eslint-disable @typescript-eslint/no-unused-vars */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ProductsManagement() {\n    _s();\n    const { hasPermission, addNotification } = (0,_contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__.useAdmin)();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedProducts, setSelectedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showAddProductModal, setShowAddProductModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showImportModal, setShowImportModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Fetch products from API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductsManagement.useEffect\": ()=>{\n            fetchProducts();\n        }\n    }[\"ProductsManagement.useEffect\"], []);\n    const fetchProducts = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await fetch('/api/admin/products', {\n                headers: {\n                    'Authorization': \"Bearer \".concat(localStorage.getItem('adminToken')),\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error('Failed to fetch products');\n            }\n            const data = await response.json();\n            if (data.success) {\n                // Transform API data to match our interface\n                const transformedProducts = data.data.products.map((product)=>{\n                    var _product_priceRange, _product_featuredImage, _product_images_, _product_images;\n                    return {\n                        id: product._id,\n                        name: product.title,\n                        category: product.category,\n                        price: ((_product_priceRange = product.priceRange) === null || _product_priceRange === void 0 ? void 0 : _product_priceRange.min) || 0,\n                        stock: product.totalInventory || 0,\n                        status: product.status,\n                        imageUrl: ((_product_featuredImage = product.featuredImage) === null || _product_featuredImage === void 0 ? void 0 : _product_featuredImage.url) || ((_product_images = product.images) === null || _product_images === void 0 ? void 0 : (_product_images_ = _product_images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.url) || '/images/placeholder.jpg',\n                        description: product.description || '',\n                        createdAt: new Date(product.createdAt).toLocaleDateString(),\n                        updatedAt: new Date(product.updatedAt).toLocaleDateString(),\n                        collections: product.collections || []\n                    };\n                });\n                setProducts(transformedProducts);\n            } else {\n                throw new Error(data.message || 'Failed to fetch products');\n            }\n        } catch (error) {\n            console.error('Failed to fetch products:', error);\n            addNotification({\n                type: 'error',\n                title: 'Error',\n                message: 'Failed to load products'\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const categories = [\n        'all',\n        'fireplaces',\n        'garden',\n        'architectural',\n        'decorative'\n    ];\n    const statuses = [\n        'all',\n        'active',\n        'inactive',\n        'draft'\n    ];\n    const filteredProducts = products.filter((product)=>{\n        const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) || product.description.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;\n        const matchesStatus = selectedStatus === 'all' || product.status === selectedStatus;\n        return matchesSearch && matchesCategory && matchesStatus;\n    });\n    const handleSelectProduct = (productId)=>{\n        setSelectedProducts((prev)=>prev.includes(productId) ? prev.filter((id)=>id !== productId) : [\n                ...prev,\n                productId\n            ]);\n    };\n    const handleSelectAll = ()=>{\n        if (selectedProducts.length === filteredProducts.length) {\n            setSelectedProducts([]);\n        } else {\n            setSelectedProducts(filteredProducts.map((p)=>p.id));\n        }\n    };\n    const handleDeleteProduct = async (productId)=>{\n        if (!hasPermission('products', 'delete')) {\n            addNotification({\n                type: 'error',\n                title: 'Permission Denied',\n                message: 'You do not have permission to delete products'\n            });\n            return;\n        }\n        if (confirm('Are you sure you want to delete this product?')) {\n            try {\n                // Simulate API call\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                setProducts((prev)=>prev.filter((p)=>p.id !== productId));\n                addNotification({\n                    type: 'success',\n                    title: 'Product Deleted',\n                    message: 'Product has been successfully deleted'\n                });\n            } catch (error) {\n                addNotification({\n                    type: 'error',\n                    title: 'Error',\n                    message: 'Failed to delete product'\n                });\n            }\n        }\n    };\n    const handleBulkDelete = async ()=>{\n        if (!hasPermission('products', 'delete')) {\n            addNotification({\n                type: 'error',\n                title: 'Permission Denied',\n                message: 'You do not have permission to delete products'\n            });\n            return;\n        }\n        if (selectedProducts.length === 0) return;\n        if (confirm(\"Are you sure you want to delete \".concat(selectedProducts.length, \" products?\"))) {\n            try {\n                // Simulate API call\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                setProducts((prev)=>prev.filter((p)=>!selectedProducts.includes(p.id)));\n                setSelectedProducts([]);\n                addNotification({\n                    type: 'success',\n                    title: 'Products Deleted',\n                    message: \"\".concat(selectedProducts.length, \" products have been deleted\")\n                });\n            } catch (error) {\n                addNotification({\n                    type: 'error',\n                    title: 'Error',\n                    message: 'Failed to delete products'\n                });\n            }\n        }\n    };\n    const handleAddProduct = ()=>{\n        setShowAddProductModal(true);\n    };\n    const handleImport = ()=>{\n        setShowImportModal(true);\n    };\n    const handleExport = async ()=>{\n        try {\n            addNotification({\n                type: 'info',\n                title: 'Export Started',\n                message: 'Your product export is being prepared...'\n            });\n            // Simulate export process\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // Create CSV content\n            const csvContent = [\n                [\n                    'Name',\n                    'Category',\n                    'Price',\n                    'Stock',\n                    'Status'\n                ],\n                ...filteredProducts.map((product)=>[\n                        product.name,\n                        product.category,\n                        product.price.toString(),\n                        product.stock.toString(),\n                        product.status\n                    ])\n            ].map((row)=>row.join(',')).join('\\n');\n            // Create and download file\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: 'text/csv'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"products-export-\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            document.body.appendChild(a);\n            a.click();\n            document.body.removeChild(a);\n            window.URL.revokeObjectURL(url);\n            addNotification({\n                type: 'success',\n                title: 'Export Complete',\n                message: 'Products have been exported successfully'\n            });\n        } catch (error) {\n            addNotification({\n                type: 'error',\n                title: 'Export Failed',\n                message: 'Failed to export products'\n            });\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().loading),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().spinner)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading products...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n            lineNumber: 251,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productsPage),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().title),\n                                children: \"Products Management\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().subtitle),\n                                children: \"Manage your product catalog, inventory, and pricing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().headerActions),\n                        children: [\n                            hasPermission('products', 'create') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().primaryButton),\n                                onClick: handleAddProduct,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Product\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryButton),\n                                onClick: handleImport,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Import\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryButton),\n                                onClick: handleExport,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().toolbar),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().searchContainer),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().searchIcon)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Search products...\",\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().searchInput)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filters),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterButton), \" \").concat(showFilters ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().active) : ''),\n                                onClick: ()=>setShowFilters(!showFilters),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Filters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this),\n                            showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterDropdown),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterGroup),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedCategory,\n                                                onChange: (e)=>setSelectedCategory(e.target.value),\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: category,\n                                                        children: category === 'all' ? 'All Categories' : category\n                                                    }, category, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().filterGroup),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: selectedStatus,\n                                                onChange: (e)=>setSelectedStatus(e.target.value),\n                                                children: statuses.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: status,\n                                                        children: status === 'all' ? 'All Statuses' : status\n                                                    }, status, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 9\n                    }, this),\n                    selectedProducts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().bulkActions),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().selectedCount),\n                                children: [\n                                    selectedProducts.length,\n                                    \" selected\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().bulkDeleteButton),\n                                onClick: handleBulkDelete,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Delete Selected\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().tableContainer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().table),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedProducts.length === filteredProducts.length && filteredProducts.length > 0,\n                                                onChange: handleSelectAll\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Product\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Category\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Price\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Stock\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Updated\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                children: filteredProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: selectedProducts.includes(product.id),\n                                                    onChange: ()=>handleSelectProduct(product.id)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productCell),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productImage),\n                                                            children: product.imageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: product.imageUrl,\n                                                                alt: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().imagePlaceholder),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productInfo),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productName),\n                                                                    children: product.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().productDescription),\n                                                                    children: product.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                    lineNumber: 399,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().category),\n                                                    children: product.category\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().price),\n                                                    children: [\n                                                        \"$\",\n                                                        product.price.toLocaleString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().stock), \" \").concat(product.stock === 0 ? (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().outOfStock) : ''),\n                                                    children: product.stock\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"\".concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().status), \" \").concat((_page_module_css__WEBPACK_IMPORTED_MODULE_3___default())[product.status]),\n                                                    children: product.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().date),\n                                                    children: new Date(product.updatedAt).toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actions),\n                                                    children: [\n                                                        hasPermission('products', 'read') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                                            title: \"View\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        hasPermission('products', 'update') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                                            title: \"Edit\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        hasPermission('products', 'delete') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                                            title: \"Delete\",\n                                                            onClick: ()=>handleDeleteProduct(product.id),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().actionButton),\n                                                            title: \"More\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, product.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, this),\n                    filteredProducts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().emptyState),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().emptyIcon),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"No products found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Try adjusting your search or filter criteria\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, this),\n            showAddProductModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modal),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalContent),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalHeader),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    children: \"Add New Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().closeButton),\n                                    onClick: ()=>setShowAddProductModal(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalBody),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Add Product form will be implemented here.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"This will include fields for:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Product name and description\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Category and pricing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Inventory management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Image uploads\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"SEO settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalActions),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryButton),\n                                    onClick: ()=>setShowAddProductModal(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().primaryButton),\n                                    children: \"Create Product\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 468,\n                columnNumber: 9\n            }, this),\n            showImportModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modal),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalContent),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalHeader),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    children: \"Import Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().closeButton),\n                                    onClick: ()=>setShowImportModal(false),\n                                    children: \"\\xd7\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalBody),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Import products from CSV file.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().uploadArea),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_Eye_Filter_MoreHorizontal_Plus_Search_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: 48\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Drag and drop your CSV file here, or click to browse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"file\",\n                                            accept: \".csv\",\n                                            style: {\n                                                display: 'none'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().importInstructions),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"CSV Format Requirements:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Name, Category, Price, Stock, Status columns required\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Price should be numeric (e.g., 29.99)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Stock should be integer (e.g., 100)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Status should be: active, inactive, or draft\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                            lineNumber: 527,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().modalActions),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().secondaryButton),\n                                    onClick: ()=>setShowImportModal(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 536,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: (_page_module_css__WEBPACK_IMPORTED_MODULE_3___default().primaryButton),\n                                    children: \"Import Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                            lineNumber: 535,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                    lineNumber: 508,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n                lineNumber: 507,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\app\\\\admin\\\\products\\\\page.tsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductsManagement, \"Sd8k+dtmGd1AIybCnWSGiosquAY=\", false, function() {\n    return [\n        _contexts_AdminContext__WEBPACK_IMPORTED_MODULE_2__.useAdmin\n    ];\n});\n_c = ProductsManagement;\nvar _c;\n$RefreshReg$(_c, \"ProductsManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/products/page.tsx\n"));

/***/ })

});