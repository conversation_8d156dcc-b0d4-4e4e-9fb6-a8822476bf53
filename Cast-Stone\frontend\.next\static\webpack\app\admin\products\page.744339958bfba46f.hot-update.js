"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/products/page",{

/***/ "(app-pages-browser)/./src/contexts/AdminContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/AdminContext.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdminProvider: () => (/* binding */ AdminProvider),\n/* harmony export */   useAdmin: () => (/* binding */ useAdmin)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AdminProvider,useAdmin auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst initialState = {\n    admin: null,\n    isLoading: false,\n    sidebarCollapsed: false,\n    notifications: []\n};\nconst adminReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_ADMIN':\n            return {\n                ...state,\n                admin: action.payload,\n                isLoading: false\n            };\n        case 'CLEAR_ADMIN':\n            return {\n                ...state,\n                admin: null,\n                isLoading: false\n            };\n        case 'SET_LOADING':\n            return {\n                ...state,\n                isLoading: action.payload\n            };\n        case 'TOGGLE_SIDEBAR':\n            return {\n                ...state,\n                sidebarCollapsed: !state.sidebarCollapsed\n            };\n        case 'SET_SIDEBAR_COLLAPSED':\n            return {\n                ...state,\n                sidebarCollapsed: action.payload\n            };\n        case 'ADD_NOTIFICATION':\n            return {\n                ...state,\n                notifications: [\n                    ...state.notifications,\n                    {\n                        ...action.payload,\n                        id: Math.random().toString(36).substr(2, 9),\n                        timestamp: new Date()\n                    }\n                ]\n            };\n        case 'REMOVE_NOTIFICATION':\n            return {\n                ...state,\n                notifications: state.notifications.filter((n)=>n.id !== action.payload)\n            };\n        case 'CLEAR_NOTIFICATIONS':\n            return {\n                ...state,\n                notifications: []\n            };\n        default:\n            return state;\n    }\n};\nconst AdminContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nconst AdminProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(adminReducer, initialState);\n    // Load admin data on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminProvider.useEffect\": ()=>{\n            const loadAdminData = {\n                \"AdminProvider.useEffect.loadAdminData\": async ()=>{\n                    const token = localStorage.getItem('adminToken');\n                    if (!token) return;\n                    dispatch({\n                        type: 'SET_LOADING',\n                        payload: true\n                    });\n                    try {\n                        const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n                        const response = await fetch(\"\".concat(API_BASE_URL, \"/admin/profile\"), {\n                            headers: {\n                                'Authorization': \"Bearer \".concat(token)\n                            }\n                        });\n                        if (response.ok) {\n                            const data = await response.json();\n                            dispatch({\n                                type: 'SET_ADMIN',\n                                payload: data.admin\n                            });\n                        } else {\n                            localStorage.removeItem('adminToken');\n                            dispatch({\n                                type: 'CLEAR_ADMIN'\n                            });\n                        }\n                    } catch (error) {\n                        console.error('Failed to load admin data:', error);\n                        localStorage.removeItem('adminToken');\n                        dispatch({\n                            type: 'CLEAR_ADMIN'\n                        });\n                    }\n                }\n            }[\"AdminProvider.useEffect.loadAdminData\"];\n            loadAdminData();\n        }\n    }[\"AdminProvider.useEffect\"], []);\n    // Load sidebar state from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminProvider.useEffect\": ()=>{\n            const savedSidebarState = localStorage.getItem('adminSidebarCollapsed');\n            if (savedSidebarState) {\n                dispatch({\n                    type: 'SET_SIDEBAR_COLLAPSED',\n                    payload: JSON.parse(savedSidebarState)\n                });\n            }\n        }\n    }[\"AdminProvider.useEffect\"], []);\n    // Save sidebar state to localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminProvider.useEffect\": ()=>{\n            localStorage.setItem('adminSidebarCollapsed', JSON.stringify(state.sidebarCollapsed));\n        }\n    }[\"AdminProvider.useEffect\"], [\n        state.sidebarCollapsed\n    ]);\n    const hasPermission = (resource, action)=>{\n        if (!state.admin) return false;\n        if (state.admin.role === 'super_admin') return true;\n        const resourcePermissions = state.admin.permissions[resource];\n        if (!resourcePermissions) return false;\n        return resourcePermissions[action] || false;\n    };\n    const addNotification = (notification)=>{\n        dispatch({\n            type: 'ADD_NOTIFICATION',\n            payload: notification\n        });\n        const id = Math.random().toString(36).substr(2, 9);\n        setTimeout(()=>{\n            dispatch({\n                type: 'REMOVE_NOTIFICATION',\n                payload: id\n            });\n        }, 5000);\n    };\n    const removeNotification = (id)=>{\n        dispatch({\n            type: 'REMOVE_NOTIFICATION',\n            payload: id\n        });\n    };\n    const logout = async ()=>{\n        try {\n            const token = localStorage.getItem('adminToken');\n            if (token) {\n                const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n                await fetch(\"\".concat(API_BASE_URL, \"/admin/logout\"), {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': \"Bearer \".concat(token)\n                    }\n                });\n            }\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            localStorage.removeItem('adminToken');\n            dispatch({\n                type: 'CLEAR_ADMIN'\n            });\n            window.location.href = '/admin/login';\n        }\n    };\n    const apiCall = async function(url) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const token = localStorage.getItem('adminToken');\n        const API_BASE_URL = \"http://localhost:5000/api\" || 0;\n        const headers = {\n            'Content-Type': 'application/json',\n            ...token ? {\n                'Authorization': \"Bearer \".concat(token)\n            } : {},\n            ...options.headers || {}\n        };\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL).concat(url), {\n                ...options,\n                headers\n            });\n            const data = await response.json();\n            return data;\n        } catch (error) {\n            return {\n                success: false,\n                message: 'Network error',\n                error\n            };\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdminContext.Provider, {\n        value: {\n            state,\n            dispatch,\n            hasPermission,\n            addNotification,\n            removeNotification,\n            logout,\n            apiCall\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Patricks web\\\\Cast-Stone\\\\frontend\\\\src\\\\contexts\\\\AdminContext.tsx\",\n        lineNumber: 242,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AdminProvider, \"s3jE+e7wLGXN/2uWqdAG2uRSMfA=\");\n_c = AdminProvider;\nconst useAdmin = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AdminContext);\n    if (!context) {\n        throw new Error('useAdmin must be used within an AdminProvider');\n    }\n    return context;\n};\n_s1(useAdmin, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AdminProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AdminContext.tsx\n"));

/***/ })

});