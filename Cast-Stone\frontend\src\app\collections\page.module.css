.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.hero {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.heroContent {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.heroTitle {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.heroSubtitle {
  font-size: 20px;
  opacity: 0.9;
  margin: 0;
  line-height: 1.5;
}

.collectionsSection {
  flex: 1;
  padding: 80px 0;
  background: var(--color-gray-50);
}

.collectionsContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.collectionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 40px;
}

.collectionCard {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.collectionCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.collectionLink {
  display: block;
  text-decoration: none;
  color: inherit;
}

.collectionImage {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.collectionCard:hover .image {
  transform: scale(1.05);
}

.collectionOverlay {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.collectionIcon {
  color: var(--color-primary);
}

.collectionContent {
  padding: 24px;
}

.collectionTitle {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 12px 0;
  line-height: 1.3;
}

.collectionDescription {
  font-size: 16px;
  color: var(--color-gray-600);
  line-height: 1.6;
  margin: 0 0 16px 0;
}

.collectionMeta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.levelBadge {
  background: var(--color-primary-light);
  color: var(--color-primary-dark);
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.childrenCount,
.productCount {
  font-size: 14px;
  color: var(--color-gray-600);
}

.subCollections {
  padding: 0 24px 24px 24px;
  border-top: 1px solid var(--color-border);
}

.subCollectionsTitle {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 20px 0 16px 0;
}

.subCollectionsGrid {
  display: grid;
  gap: 8px;
}

.subCollectionLink {
  text-decoration: none;
  color: inherit;
}

.subCollectionCard {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: var(--color-gray-50);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.subCollectionCard:hover {
  background: var(--color-gray-100);
  transform: translateX(4px);
}

.subCollectionIcon {
  color: var(--color-gray-600);
  display: flex;
  align-items: center;
}

.subCollectionInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.subCollectionTitle {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-gray-900);
}

.subCollectionLevel {
  font-size: 12px;
  color: var(--color-gray-600);
}

.subCollectionArrow {
  color: var(--color-gray-400);
  transition: transform 0.2s ease;
}

.subCollectionCard:hover .subCollectionArrow {
  transform: translateX(4px);
}

.loadingContainer,
.errorContainer,
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-border);
  border-top: 4px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorMessage {
  color: var(--color-error);
  font-size: 16px;
  margin: 0 0 16px 0;
}

.retryButton {
  padding: 12px 24px;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retryButton:hover {
  background: var(--color-primary-dark);
}

.emptyState svg {
  color: var(--color-gray-400);
  margin-bottom: 16px;
}

.emptyState h3 {
  font-size: 24px;
  color: var(--color-gray-700);
  margin: 0 0 8px 0;
}

.emptyState p {
  font-size: 16px;
  color: var(--color-gray-600);
  margin: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .heroTitle {
    font-size: 36px;
  }
  
  .heroSubtitle {
    font-size: 18px;
  }
  
  .collectionsGrid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .collectionCard {
    border-radius: 12px;
  }
  
  .collectionContent {
    padding: 20px;
  }
  
  .subCollections {
    padding: 0 20px 20px 20px;
  }
}
